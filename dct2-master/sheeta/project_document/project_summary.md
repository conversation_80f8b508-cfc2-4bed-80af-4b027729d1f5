# Sheeta项目总结

## 核心目标
Sheeta是一个后台管理系统，主要用于设备数据采集、监控和管理。根据代码分析，该系统的核心目标包括：

1. 提供设备数据采集和传输功能
2. 实现设备远程监控和管理
3. 支持系统监控和运维
4. 提供用户权限和角色管理
5. 支持串口通信和网络通信两种方式连接设备

## 主要技术栈

1. **后端技术**：
   - Spring Boot 2.1.1：作为主要的应用框架
   - MyBatis：用于数据持久层
   - Spring Security：用于认证和授权
   - Redis：用于缓存和会话管理
   - Netty：用于网络通信
   - Kafka：用于消息队列
   - WebSocket：用于实时通信
   - Quartz：用于定时任务调度
   - JWT：用于令牌生成与解析
   - Swagger：用于API文档生成

2. **数据库**：
   - MySQL：主要数据库

3. **硬件通信**：
   - 串口通信：使用RXTX库实现
   - TCP/IP通信：使用Netty框架实现

## 关键模块

1. **设备管理模块** (`com.xhjt.project.device`)：
   - 设备信息管理
   - 设备属性配置
   - 设备连接状态监控
   - 串口配置管理
   - 设备数据传输属性管理

2. **网络通信模块** (`com.xhjt.project.netty`)：
   - TCP服务器实现
   - 通道管理
   - 数据编解码
   - 消息处理

3. **系统监控模块** (`com.xhjt.project.monitor`)：
   - 服务器监控
   - 操作日志
   - 登录信息
   - 在线用户管理
   - 定时任务管理

4. **系统管理模块** (`com.xhjt.project.system`，推测)：
   - 用户管理
   - 角色管理
   - 菜单管理
   - 部门管理
   - 岗位管理

5. **数据采集模块**：
   - 采集节点配置
   - 采集模块配置
   - 数据统计

6. **快照模块** (`com.xhjt.project.snapshot`，推测)：
   - 快照通道管理
   - 快照传输
   - 快照记录

7. **安全框架** (`com.xhjt.framework.security`)：
   - 认证
   - 授权
   - 安全配置

8. **通用功能模块**：
   - Redis缓存
   - 任务调度
   - 拦截器
   - 数据源配置
   - 异常处理

## 系统特点

1. 支持多种设备连接方式（串口、网络）
2. 提供完善的权限管理机制
3. 具有数据采集和传输功能
4. 支持设备状态实时监控
5. 提供系统运行状态监控
6. 具备定时任务调度能力
7. 支持数据统计和日志记录 