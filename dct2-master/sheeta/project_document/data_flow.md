# Sheeta系统数据流程分析

## 数据流程图

```mermaid
flowchart TB
    %% 设备和采集终端
    Device1[设备1] -->|串口通信| SerialPort[串口]
    Device2[设备2] -->|串口通信| SerialPort
    Device3[设备3] -->|TCP通信| TcpServer
    
    %% 系统初始化
    SystemLoad -->|初始化串口| SerialPortUtil
    SystemLoad -->|初始化TCP服务| TcpServer
    SystemLoad -->|缓存设备| DeviceUtil
    SystemLoad -->|启动串口服务| RtxtService
    
    %% 数据采集层
    SerialPort -->|SerialPortUtil.readData| SerialPortListener[串口监听器]
    TcpServer -->|Netty| ChannelManagerHandler
    
    %% 数据处理层
    SerialPortListener -->|RtxtService.serialEvent| ClientListernerUtil
    ChannelManagerHandler -->|channelRead| ClientListernerUtil
    
    %% 数据统计与监控
    ClientListernerUtil -->|redisCompare| RedisCache1["Redis缓存<br>(CJ_SB_*: 设备采集数据统计)"]
    ClientListernerUtil -->|redisSnapshotCompare| RedisCache2["Redis缓存<br>(CJ_KZ_*: 快照采集数据统计)"]
    ClientListernerUtil -->|连接状态检查| RedisCache3["Redis缓存<br>(CJ_CK_*: 串口连接状态)"]
    ClientListernerUtil -->|岸端预览标记| RedisCache4["Redis缓存<br>(RT_DB_*: 岸端实时预览状态)"]
    
    %% 数据实时处理
    ClientListernerUtil -->|webSocketSendMsg| WebSocketServer[WebSocket服务器]
    ClientListernerUtil -->|parseView/parseSerilView| Dola[Dola解析服务]
    ClientListernerUtil -->|syncAllRawData| StoreService
    
    %% Kafka消息队列
    StoreService -->|send2Kafka| KafkaTopic1["Kafka Topic:<br>ship_device_source_data_topic<br>(设备源数据)"]
    StoreService -->|save2Txt| FileStorage[文本文件存储]
    StoreService -->|sendSync2Kafka| KafkaTopic2["Kafka Topic:<br>ship_sync_data_topic<br>(同步数据)"]
    StoreService -->|sendRawData2Kafka| KafkaTopic3["Kafka Topic:<br>ship_raw_data_topic<br>(原始数据)"]
    
    %% 定时任务
    ScheduledTask -->|统计数据入库| DataStatistics[数据统计表]
    ScheduledTask -->|连接状态监控| DeviceStatusUpdate[设备状态更新]
    ScheduledTask -->|sys2Msg| ShoreSync[岸端同步]
    
    %% 数据消费
    KafkaTopic1 --> DataConsumer[数据消费者]
    KafkaTopic2 --> SyncConsumer[同步数据消费者]
    KafkaTopic3 --> RawDataConsumer[原始数据消费者]
    WebSocketServer --> FrontEnd[前端实时显示]
    RedisCache1 -.->|定时统计| DataStatistics
```

## 数据流程详细说明

### 1. 系统初始化与数据采集入口

#### 1.1 系统初始化过程

1. **系统启动加载 (SystemLoad)**
   - 核心类：`SystemLoad.java`
   - 文件路径：`src/main/java/com/xhjt/framework/init/SystemLoad.java`
   - 主要功能：
     - 系统基础功能初始化
     - 设备连接状态处理
     - 串口发现与初始化
     - 设备缓存加载
     - TCP服务器启动
     - 串口监听服务启动
   - 关键方法：
     ```java
     public void run(String... args) {
         // 连接状态处理
         acquisitionMiddlewareService.connectStatusInvalid();
         acquisitionMiddlewareService.delete4Invalid();
         
         // 串口发现与清理
         SerialPortUtil.findSystemAllSerialPort();
         SerialPortUtil.cleanOpenMap();
         
         // 缓存所有的设备
         DeviceUtil.addAllDevice();
         RedisDeviceUtil.addAllDevice();
         
         // 启动TCP服务器
         TcpServer tcpServer = TcpServer.getTcpServer();
         tcpServer.bind(22333);
         
         // 启动串口监听服务
         RtxtService inputService = SpringUtils.getBean(RtxtService.class);
         inputService.handleAllService();
     }
     ```

#### 1.2 串口通信流程

1. **串口发现与初始化**
   - 核心类：`SerialPortUtil.java`
   - 文件路径：`src/main/java/com/xhjt/common/SerialPortUtil.java`
   - 关键方法：
     - `findSystemAllSerialPort()`：枚举系统中所有可用串口
     - `openComPort(String portName, int b, int d, int s, int p)`：打开串口并设置参数
       - b：波特率（baudrate）
       - d：数据位（datebits）
       - s：停止位（stopbits）
       - p：校验位（parity）

2. **串口服务启动**
   - 核心类：`RtxtService.java`
   - 文件路径：`src/main/java/com/xhjt/framework/serialport/RtxtService.java`
   - 关键方法：
     - `handleAllService()`：启动所有串口设备服务
     - `toListenerSerialPort(DeviceEntity)`：为指定设备启动串口监听
   - 实现过程：
     ```java
     public void handleAllService() {
         // 查找所有启用的串口设备
         DeviceEntity deviceEntity = new DeviceEntity();
         deviceEntity.setConnectType(1); // 串口类型
         deviceEntity.setEnable(1);      // 启用状态
         List<DeviceEntity> list = deviceService.selectDeviceList(deviceEntity);
         
         // 为每个设备设置串口监听
         for (DeviceEntity device: list) {
             toListenerSerialPort(device);
         }
     }
     ```

3. **串口数据接收**
   - 核心类：`RtxtService.java`中的匿名内部类`SerialPortEventListener`
   - 关键方法：`serialEvent(SerialPortEvent)`
   - 执行流程：
     ```java
     public void serialEvent(SerialPortEvent arg0) {
         // 当有数据到达时
         if (arg0.getEventType() == SerialPortEvent.DATA_AVAILABLE) {
             // 读取串口数据
             byte[] bytes = SerialPortUtil.readData(serialPort, 20);
             
             // 记录连接状态到Redis
             redisCache.setCacheObject("CJ_CK_"+deviceEntity.getSerialPort(), "1", 5, TimeUnit.MINUTES);
             
             // 数据统计
             clientListernerUtil.redisCompare(bytes);
             
             // 实时数据发送
             if (ClientListernerUtil.states == 0) {
                 WebSocketServer.sendRealTimeData(new String(bytes, StandardCharsets.UTF_8), deviceEntity.getSerialPort());
             }
             
             // 解析预览
             if (ClientListernerUtil.states == 1) {
                 KafkaMessage kafkaMessage = getReceiveMessage(deviceEntity, bytes, time);
                 clientListernerUtil.parseSerilView(kafkaMessage, deviceEntity.getSerialPort());
             }
             
             // 数据存储和传输
             if (deviceEntity.getEnable()==1 || deviceEntity.getTransferStatus()==1) {
                 KafkaMessage kafkaMessage = getReceiveMessage(deviceEntity, bytes, time);
                 // 发送数据到Kafka
                 storeService.send2Kafka(kafkaMessage);
                 // 存入txt文件
                 storeService.save2Txt(kafkaMessage);
             }
         }
     }
     ```

#### 1.3 TCP网络通信流程

1. **TCP服务器初始化**
   - 核心类：`TcpServer.java`
   - 文件路径：`src/main/java/com/xhjt/project/netty/server/TcpServer.java`
   - 关键方法：`bind(int port)`：绑定端口，启动TCP服务器
   - 在系统初始化时执行：`tcpServer.bind(22333);`

2. **TCP数据接收**
   - 核心类：`ChannelManagerHandler.java`
   - 文件路径：`src/main/java/com/xhjt/project/netty/handler/ChannelManagerHandler.java`
   - 关键方法：`channelRead(ChannelHandlerContext ctx, Object msg)`
   - 执行流程：接收TCP数据，解析为AmProtocol对象，并交给ClientListernerUtil处理

### 2. 数据处理与分发

#### 2.1 数据处理核心 - ClientListernerUtil

- 核心类：`ClientListernerUtil.java`
- 文件路径：`src/main/java/com/xhjt/common/ClientListernerUtil.java`
- 主要功能和核心方法：

  1. **数据统计记录 (`redisCompare`方法)**
     ```java
     public synchronized void redisCompare(byte[] contents) {
         // 如果数据为空则不处理
         if (ArrayUtils.isEmpty(contents)){
             return;
         }
         
         Date nowDate = new Date();
         int cjLength = contents.length;  // 获取数据长度
         
         // 格式化当前分钟时间戳
         String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", nowDate);
         long ct = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm").getTime();
         
         // 检查Redis中是否已存在该分钟的统计数据
         if (redisCache.getCacheObject("CJ_SB_"+ct) != null){
             // 已存在，累加数据长度
             int datas = redisCache.getCacheObject("CJ_SB_"+ct);
             redisCache.setCacheObject("CJ_SB_"+ct, datas+cjLength);
         } else {
             // 不存在，检查是否有历史数据需要处理
             if (redisCache.keys("CJ_SB_*") != null && redisCache.keys("CJ_SB_*").size() > 0){
                 // 将历史数据入库，并补充中间缺失的分钟
                 redisCache.keys("CJ_SB_*").iterator().forEachRemaining(s -> {
                     // 处理历史数据
                     String longTime = s.split("_")[2];
                     Date date = new Date();
                     date.setTime(Long.parseLong(longTime));
                     // 存储到数据库
                     int dataNew = redisCache.getCacheObject(s);
                     addStatis(dataNew, date, 0);
                     // 删除Redis记录
                     redisCache.deleteObject(s);
                     // 补充中间缺失的分钟数据
                     try {
                         dateForAfter(date, nowDate, 1);
                     } catch (ParseException e) {
                         e.printStackTrace();
                     }
                 });
             }
             // 创建新的Redis记录
             redisCache.setCacheObject("CJ_SB_"+ct, cjLength);
         }
     }
     ```

  2. **快照数据统计记录 (`redisSnapshotCompare`方法)**
     - 与redisCompare类似，但针对快照数据，使用"CJ_KZ_"前缀的Redis键

  3. **WebSocket实时数据发送 (`webSocketSendMsg`方法)**
     ```java
     public void webSocketSendMsg(AmProtocol amProtocol) {
         int type = ClientListernerUtil.type;
         // 根据设备类型选择不同的数据格式发送
         if (DeviceTypeEnum.GPS_IN.getValue().equals(type) || DeviceTypeEnum.COMPASS_IN.getValue().equals(type)){
             // 十六进制字符串格式
             WebSocketServer.sendRealTimeData(bytesTo16String(amProtocol.getContent()), amProtocol.getMac());
         } else {
             // UTF-8字符串格式
             WebSocketServer.sendRealTimeData(new String(amProtocol.getContent(), StandardCharsets.UTF_8), amProtocol.getMac());
         }
     }
     ```

  4. **数据解析预览 (`parseView`方法)**
     ```java
     public void parseView(AmProtocol amProtocol) {
         int type = ClientListernerUtil.type;
         String receiveMessage;
         
         // 根据设备类型选择不同的解析方式
         if (DeviceTypeEnum.ATTITUDE.getValue().equals(type)) {
             // 姿态数据，十进制字符串
             receiveMessage = bytesToTenString(amProtocol.getContent());
         } else if (DeviceTypeEnum.GPS_IN.getValue().equals(type) || DeviceTypeEnum.COMPASS_IN.getValue().equals(type)) {
             // 内置设备，十六进制字符串
             receiveMessage = bytesTo16String(amProtocol.getContent());
         } else {
             // 其他设备，UTF-8字符串
             receiveMessage = new String(amProtocol.getContent(), StandardCharsets.UTF_8);
             // 处理特殊格式
             if (receiveMessage.contains("@@")) {
                 receiveMessage = receiveMessage.substring(8);
             }
         }
         
         // 处理换行和制表符
         if (receiveMessage.contains("\n\t")){
             receiveMessage = receiveMessage.replace("\n","");
             receiveMessage = receiveMessage.replace("\t","");
         }
         
         // 创建Kafka消息对象
         KafkaMessage kafkaMessage = new KafkaMessage(type, DeviceUtil.getDeviceCode(amProtocol.getMac()), 
                                                      receiveMessage, 10, amProtocol.getTime());
         
         // 解析预览处理逻辑
         if (flag == false && amProtocol.getMac().equalsIgnoreCase(sbMac)){
             // 收集数据到解析队列
             if (DeviceTypeEnum.GPS_IN.getValue().equals(type) || DeviceTypeEnum.COMPASS_IN.getValue().equals(type)){
                 kafkaMessageList.add(kafkaMessage);
                 this.flag = true;
             } else {
                 if (kafkaMessageList.size() <= 10){
                     kafkaMessageList.add(kafkaMessage);
                     if (kafkaMessageList.size() == 10){
                         this.flag = true;
                     }
                 }
             }
         }
         
         // 达到解析条件，调用解析服务
         if (flag){
             if (kafkaMessageList != null && kafkaMessageList.size() > 0){
                 // 调用Dola接口进行解析
                 String jsonResult = dolaApi.parseMsgList(JSONObject.toJSONString(kafkaMessageList));
                 // 返回解析结果到前端
                 WebSocketServer.sendRealTimeData(jsonResult, amProtocol.getMac());
                 // 清理集合数据
                 kafkaMessageList.clear();
                 if (closeFlag){
                     this.flag = false;
                 }
             }
         }
     }
     ```

  5. **原始数据同步 (`syncAllRawData`方法)**
     ```java
     public void syncAllRawData(AmProtocol amProtocol) {
         // 当岸端有开启实时预览时才做处理
         if(redisCache.getCacheObject("RT_DB_"+amProtocol.getMac()) != null){
             StoreService storeService = SpringUtils.getBean(StoreService.class);
             String messages;
             
             // 根据设备类型选择不同的数据格式
             if (DeviceTypeEnum.GPS_IN.getValue().equals(ClientListernerUtil.type) || 
                 DeviceTypeEnum.COMPASS_IN.getValue().equals(ClientListernerUtil.type)){
                 messages = bytesTo16String(amProtocol.getContent());
             } else {
                 messages = new String(amProtocol.getContent(), StandardCharsets.UTF_8);
             }
             
             // 创建采集消息对象
             AcquisitionMessage acquisitionMessage = new AcquisitionMessage();
             acquisitionMessage.setContent(messages);
             acquisitionMessage.setMac(amProtocol.getMac());
             
             // 创建Kafka消息并发送
             KafkaMessage kafkaMessage = new KafkaMessage("raws", 
                                                          JSONObject.toJSONString(acquisitionMessage), 
                                                          30, System.currentTimeMillis());
             storeService.sendRawData2Kafka(kafkaMessage);
         }
     }
     ```

#### 2.2 Redis缓存使用

- 核心类：`RedisCache.java`
- 文件路径：`src/main/java/com/xhjt/framework/redis/RedisCache.java`
- Redis参数定义：`RedisParameter.java`
- 文件路径：`src/main/java/com/xhjt/common/RedisParameter.java`

- 主要Redis键：
  1. **数据统计相关键**：
     - `CJ_SB_[timestamp]`：设备采集数据统计（按分钟），值为采集数据长度
     - `CJ_KZ_[timestamp]`：快照采集数据统计（按分钟），值为采集数据长度
  
  2. **连接状态相关键**：
     - `CJ_CK_[serialPort]`：串口连接状态检查，值为"1"表示连接正常，有效期5分钟
     - 定时任务会检查该键判断串口连接状态

  3. **实时预览状态标记**：
     - `RT_DB_[mac]`：岸端实时预览状态标记，存在表示岸端开启了实时预览
     - 用于判断是否需要同步原始数据到岸端

  4. **其他系统参数**：
     - `SHIP_ENGINE_ROOM_FTP_IP`：机舱FTP IP地址
     - `SHIP_ENGINE_ROOM_FTP_PORT`：机舱FTP端口
     - `SHIP_ENGINE_ROOM_FTP_USER`：机舱FTP用户名
     - `SHIP_ENGINE_ROOM_FTP_PASSWORD`：机舱FTP密码
     - `LATEST_PICTURE_DATE_`：保存截屏时间
     - `SHIP_SN_`：船舶SN标识

#### 2.3 Kafka消息队列使用

- 核心类：`StoreService.java`
- 文件路径：`src/main/java/com/xhjt/project/netty/service/StoreService.java`
- 主要Topic：
  1. `ship_device_source_data_topic`：设备源数据Topic
     - 方法：`send2Kafka(KafkaMessage)`
     - 用于存储设备原始采集数据
  
  2. `ship_sync_data_topic`：同步数据Topic
     - 方法：`sendSync2Kafka(KafkaMessage)`
     - 用于同步数据到岸端系统
  
  3. `ship_raw_data_topic`：原始数据Topic
     - 方法：`sendRawData2Kafka(KafkaMessage)`
     - 用于传输实时预览数据

- 消息格式：
  ```java
  // KafkaMessage对象结构
  public KafkaMessage(Integer type, String code, String content, Integer cost, Long time) {
      this.type = type;        // 设备类型
      this.code = code;        // 设备编码
      this.content = content;  // 数据内容
      this.cost = cost;        // 消耗时间/处理代价
      this.time = time;        // 时间戳
  }
  ```

### 3. 定时任务与状态监控

#### 3.1 定时任务处理

- 核心类：`ScheduledTask.java`
- 文件路径：`src/main/java/com/xhjt/framework/task/ScheduledTask.java`
- 主要功能：

  1. **设备状态监控与同步**
     ```java
     public void sys2Msg() {
         // 设备同步到岸端
         List<DeviceEntity> list = deviceService.selectDeviceList(null);
         if (list != null && list.size() > 0) {
             for (DeviceEntity deviceEntity : list) {
                 if (deviceEntity.getConnectType() == 1) {
                     // 串口 - 检查Redis中的连接状态记录
                     String data = redisCache.getCacheObject("CJ_CK_" + deviceEntity.getSerialPort());
                     if (org.apache.commons.lang3.StringUtils.isNotBlank(data)) {
                         // 有数据则说明5分钟内是正常连接
                         deviceEntity.setConnectStatus(1);
                     } else {
                         deviceEntity.setConnectStatus(0);
                     }
                 } else {
                     // 采集终端 - 根据采集终端的状态更新设备状态
                     AcquisitionMiddleware am = amService.selectAcquisitionMiddlewareByMac(deviceEntity.getMac());
                     if (am != null) {
                         deviceEntity.setConnectStatus(am.getConnectStatus());
                     }
                 }
                 // 更新设备状态并同步到岸端
                 deviceService.updateDevice(deviceEntity);
                 // ...同步逻辑
             }
         }
         
         // 快照信息同步到岸端
         // ...
     }
     ```

  2. **数据统计入库**
     - 通过Redis统计数据的定时入库
     - 将`CJ_SB_[timestamp]`和`CJ_KZ_[timestamp]`的数据转移到数据库表

  3. **截屏获取**
     ```java
     public void obtainScreenShot(String channelCode) {
         try {
             snapshotHandlerService.start(channelCode);
         } catch (Throwable t) {
             logger.error("获取摄像头截屏异常。。。。{}", t);
         }
     }
     ```

### 4. 数据流程步骤说明

1. **设备数据采集**：
   - 通过串口或TCP连接接收设备数据
   - 串口通过`SerialPortUtil`类管理
   - TCP通过Netty框架和`TcpServer`类管理

2. **数据接收与初步处理**：
   - 串口数据通过串口监听器接收
   - TCP数据通过`ChannelManagerHandler`的`channelRead()`方法接收
   - 两种方式接收的数据都会转发给`ClientListernerUtil`进行处理

3. **数据统计与缓存**：
   - `ClientListernerUtil.redisCompare()`方法将接收到的数据长度统计并存入Redis
   - 按分钟统计数据量，键格式为`CJ_SB_[timestamp]`或`CJ_KZ_[timestamp]`
   - 定期将Redis中的统计数据写入数据库表`data_statistics`

4. **数据实时处理**：
   - 实时数据通过WebSocket发送到前端（`webSocketSendMsg()`方法）
   - 原始数据同步到岸端（`syncAllRawData()`方法）
   - 数据解析预览功能（`parseView()`方法）

5. **数据持久化与分发**：
   - 通过`StoreService`将数据发送到Kafka消息队列
   - 同时将原始数据保存到文本文件（`save2Txt()`方法）
   - 不同类型的数据发送到不同的Kafka Topic

6. **数据消费**：
   - 下游系统通过订阅Kafka Topic消费数据
   - 前端通过WebSocket接收实时数据并显示

### 5. 关键类和方法调用顺序

#### 5.1 串口数据流程

**系统初始化阶段**：
1. `SystemLoad.run()` → 系统启动初始化
2. `SerialPortUtil.findSystemAllSerialPort()` → 发现系统所有串口
3. `SerialPortUtil.cleanOpenMap()` → 清理已打开的串口
4. `RtxtService.handleAllService()` → 启动所有串口服务
5. `RtxtService.toListenerSerialPort(DeviceEntity)` → 为指定设备设置串口监听器
6. `SerialPortUtil.openComPort(portName, baudRate, dataBits, stopBits, parity)` → 打开串口
7. `SerialPortUtil.addOpenPort(serialPort)` → 添加到已打开串口集合
8. `SerialPortUtil.setListenerToSerialPort(serialPort, listener)` → 设置串口监听器

**数据接收与处理阶段**：
1. 串口监听器.`serialEvent(SerialPortEvent)` → 接收到串口数据事件
2. `SerialPortUtil.readData(serialPort, 20)` → 读取串口数据
3. `redisCache.setCacheObject("CJ_CK_"+serialPort, "1", 5, TimeUnit.MINUTES)` → 记录串口连接状态
4. `ClientListernerUtil.redisCompare(bytes)` → 数据统计
   - 按分钟累计数据量到Redis `CJ_SB_[timestamp]`
   - 如有需要，将历史数据入库 (`addStatis()`)
5. `WebSocketServer.sendRealTimeData(data, serialPort)` → 实时数据发送到前端
6. `RtxtService.getReceiveMessage(deviceEntity, bytes, time)` → 处理原始数据，创建KafkaMessage
7. `ClientListernerUtil.parseSerilView(kafkaMessage, serialPort)` → 解析预览（如果开启）
   - 调用Dola接口 `dolaApi.parseMsgList()`
8. `StoreService.send2Kafka(kafkaMessage)` → 发送数据到Kafka
9. `StoreService.save2Txt(kafkaMessage)` → 保存数据到文件

**定时监控阶段**：
1. `ScheduledTask.sys2Msg()` → 定时执行
2. 检查`redisCache.getCacheObject("CJ_CK_" + serialPort)` → 判断串口连接状态
3. 更新设备连接状态并同步到岸端

#### 5.2 TCP数据流程

**系统初始化阶段**：
1. `SystemLoad.run()` → 系统启动初始化
2. `TcpServer.getTcpServer()` → 获取TCP服务器实例
3. `TcpServer.bind(22333)` → 绑定TCP端口，启动服务器

**数据接收与处理阶段**：
1. `ChannelManagerHandler.channelRead(ctx, msg)` → 接收TCP数据
2. 数据解析为`AmProtocol`对象
3. `ClientListernerUtil.redisCompare(amProtocol.getContent())` → 数据统计
4. `ClientListernerUtil.webSocketSendMsg(amProtocol)` → 实时数据发送到前端
   - 根据设备类型判断数据格式（十六进制或UTF-8）
5. `ClientListernerUtil.parseView(amProtocol)` → 解析预览（如果开启）
   - 根据设备类型处理数据格式
   - 收集数据到解析队列
   - 调用Dola接口 `dolaApi.parseMsgList()`
6. `ClientListernerUtil.syncAllRawData(amProtocol)` → 同步原始数据到岸端（如果开启预览）
   - 检查`redisCache.getCacheObject("RT_DB_"+mac)`判断是否需要同步
   - 创建`AcquisitionMessage`对象
   - 通过`StoreService.sendRawData2Kafka()`发送数据
7. `StoreService.send2Kafka(kafkaMessage)` → 发送数据到Kafka Topic
8. `StoreService.save2Txt(kafkaMessage)` → 保存数据到文件

**定时监控阶段**：
1. `ScheduledTask.sys2Msg()` → 定时执行
2. 查询`AcquisitionMiddleware`对象，获取连接状态
3. 更新设备连接状态并同步到岸端

#### 5.3 数据存储和传输链路

1. **实时数据展示**：
   - `WebSocketServer.sendRealTimeData(data, id)` → 发送到前端
   - 前端WebSocket客户端接收并显示

2. **数据存储**：
   - `StoreService.save2Txt(kafkaMessage)` → 保存到文本文件
   - 文件路径通常基于设备类型、时间等信息构建

3. **Kafka消息发送**：
   - `StoreService.send2Kafka(kafkaMessage)` → 发送到`ship_device_source_data_topic`
   - `StoreService.sendSync2Kafka(kafkaMessage)` → 发送到`ship_sync_data_topic`
   - `StoreService.sendRawData2Kafka(kafkaMessage)` → 发送到`ship_raw_data_topic`

4. **数据统计链路**：
   - `ClientListernerUtil.redisCompare()` → 记录到Redis `CJ_SB_[timestamp]`
   - `ClientListernerUtil.redisSnapshotCompare()` → 记录到Redis `CJ_KZ_[timestamp]`
   - 定时任务将Redis数据转移到数据库表`data_statistics`

5. **状态监控链路**：
   - 串口连接状态：`redisCache.setCacheObject("CJ_CK_"+serialPort, "1", 5, TimeUnit.MINUTES)`
   - 定时任务检查Redis，更新设备状态表

6. **岸端同步链路**：
   - 设备状态：定时任务收集并同步
   - 原始数据：通过`syncAllRawData()`方法，发送到Kafka消息队列
   - 设备同步信息：通过`sys2Msg()`方法，发送到岸端

### 6. 总结

Sheeta系统实现了一个完整的数据采集、处理、存储和分发流程。核心数据流程为：

1. **采集层**：通过串口（SerialPortUtil）或TCP服务器（TcpServer）接收设备数据
2. **处理层**：使用ClientListernerUtil处理数据，包括统计、实时发送、解析预览和原始数据同步
3. **存储层**：通过StoreService将数据存储到文件系统和Kafka消息队列
4. **分发层**：使用WebSocket将实时数据发送到前端，使用Kafka将数据分发到其他系统
5. **监控层**：使用Redis记录连接状态和数据统计，定时任务进行状态监控和数据同步

Redis作为中间存储和状态管理工具，主要用于数据统计（CJ_SB_*，CJ_KZ_*）、连接状态检查（CJ_CK_*）和实时预览状态标记（RT_DB_*）。

Kafka作为消息队列，主要用于设备源数据（ship_device_source_data_topic）、同步数据（ship_sync_data_topic）和原始数据（ship_raw_data_topic）的传输和分发。

整个系统通过这些组件的协同工作，实现了设备数据的高效采集、处理和利用。 