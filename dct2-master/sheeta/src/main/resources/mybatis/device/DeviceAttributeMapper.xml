<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.device.mapper.DeviceAttributeMapper">

    <resultMap type="DeviceAttributeEntity" id="DeviceAttributeResult">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="label" column="label"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDeviceAttributeVo">
        select id, type, name, label, remark, create_by, create_time, update_by, update_time
		from device_attribute
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="type !=null">
                and type = #{type}
            </if>
            <if test="name !=null and name != ''">
                and name = #{name}
            </if>
        </where>
    </sql>


    <select id="selectDeviceAttribute" parameterType="DeviceAttributeEntity" resultMap="DeviceAttributeResult">
        <include refid="selectDeviceAttributeVo"/>
        <include refid="sqlwhereSearch"/>
    </select>

    <select id="selectDeviceAttributeList" parameterType="DeviceAttributeEntity" resultMap="DeviceAttributeResult">
        <include refid="selectDeviceAttributeVo"/>
        <where>
            <if test="type !=null">
                and type = #{type}
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="label != null and label != ''">
                AND label like concat('%', #{label}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <insert id="addDeviceAttribute" parameterType="DeviceAttributeEntity">
        insert into device_attribute (
        <if test="type != null ">type,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="label != null and label != '' ">label,</if>
        <if test="remark != null and remark != '' ">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="type != null ">#{type},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="label != null and label != ''">#{label},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateDeviceAttribute" parameterType="DeviceAttributeEntity">
        update device_attribute
        <set>
            <if test="type != null ">type = #{type},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="label != null and label != ''">label = #{label},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteDeviceAttributeById" parameterType="Long">
        delete from device_attribute where id = #{id}
    </delete>

    <delete id="deleteDeviceAttributeByIds" parameterType="Long">
        delete from device_attribute where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
