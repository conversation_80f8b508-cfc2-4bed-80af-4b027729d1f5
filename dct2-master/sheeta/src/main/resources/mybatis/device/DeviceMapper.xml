<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.DeviceMapper">

    <resultMap type="DeviceEntity" id="DeviceResult">
        <id property="id" column="id"/>
        <result property="mac" column="mac"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="type" column="type"/>
        <result property="cost" column="cost"/>
        <result property="enable" column="enable"/>
        <result property="ip" column="ip"/>
        <result property="port" column="port"/>
        <result property="connectStatus" column="connect_status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="compartment" column="compartment"/>
        <result property="connectType" column="connect_type"/>
        <result property="baudRate" column="baud_rate"/>
        <result property="dataBits" column="data_bits"/>
        <result property="stopBits" column="stop_bits"/>
        <result property="parity" column="parity"/>
        <result property="serialPort" column="serial_port"/>
    </resultMap>

    <sql id="selectDeviceVo">
        select
            d.id,
            d.mac,
            d.name,
            d.code,
            d.type,
            d.cost,
            d.enable,
            am.ip,
            am.port,
            d.connect_status,
            d.create_by,
            d.create_time,
            d.update_by,
            d.update_time,
            d.transfer_status,
            d.compartment,
            d.connect_type,
            d.baud_rate,
            d.data_bits,
            d.stop_bits,
            d.parity,
            d.serial_port
        from device d
            left join acquisition_middleware am on d.mac = am.mac
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="id !=null">
                and d.id = #{id}
            </if>
            <if test="name !=null and name != ''">
                and d.name = #{name}
            </if>
            <if test="code !=null and code != ''">
                and d.code = #{code}
            </if>
            <if test="ip !=null and ip != ''">
                and am.ip = #{ip}
            </if>
            <if test="port !=null and port != ''">
                and am.port = #{port}
            </if>
            <if test="connectType !=null">
                and d.connect_type = #{connectType}
            </if>
            <if test="mac !=null and mac != ''">
                AND d.mac = #{mac}
            </if>
        </where>
    </sql>

    <select id="selectDevice" parameterType="DeviceEntity" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        <include refid="sqlwhereSearch"/>
    </select>


    <select id="selectDeviceList" parameterType="DeviceEntity" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        <where>
            <if test="name != null and name != ''">
                AND d.name like concat('%', #{name}, '%')
            </if>
            <if test="code !=null and code != ''">
                AND d.code = #{code}
            </if>
            <if test="mac !=null and mac != ''">
                AND d.mac = #{mac}
            </if>
            <if test="ip != null and ip != ''">
                AND am.ip like concat('%', #{ip}, '%')
            </if>
            <if test="port !=null and port != ''">
                AND am.port = #{port}
            </if>
            <if test="connectStatus !=null ">
                AND am.connect_status = #{connectStatus}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(d.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(d.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <if test="connectType !=null">
                and d.connect_type = #{connectType}
            </if>
        </where>
    </select>

    <insert id="addDevice" parameterType="DeviceEntity">
        insert into device (
        <if test="mac != null">mac,</if>
        <if test="serialPort != null">serial_port,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="code != null and code != '' ">code,</if>
        <if test="type != null ">type,</if>
        <if test="cost != null ">cost,</if>
        <if test="enable != null">enable,</if>
        <if test="transferStatus != null">transfer_status,</if>
        <if test="connectType != null ">connect_type,</if>
        <if test="compartment != null ">compartment,</if>
        <if test="baudRate != null ">baud_rate,</if>
        <if test="dataBits != null ">data_bits,</if>
        <if test="stopBits != null">stop_bits,</if>
        <if test="parity != null">parity,</if>
        <if test="remark != null and remark != '' ">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="connectStatus != null">connect_status,</if>
        create_time
        )values(
        <if test="mac != null and mac != ''">#{mac},</if>
        <if test="serialPort != null and serialPort != ''">#{serialPort},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="code != null and code != ''">#{code},</if>
        <if test="type != null ">#{type},</if>
        <if test="cost != null ">#{cost},</if>
        <if test="enable != null ">#{enable},</if>
        <if test="transferStatus != null ">#{transferStatus},</if>
        <if test="connectType != null ">#{connectType},</if>
        <if test="compartment != null ">#{compartment},</if>
        <if test="baudRate != null ">#{baudRate},</if>
        <if test="dataBits != null ">#{dataBits},</if>
        <if test="stopBits != null">#{stopBits},</if>
        <if test="parity != null">#{parity},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="connectStatus != null ">#{connectStatus},</if>
        sysdate()
        )
    </insert>

    <update id="updateDevice" parameterType="DeviceEntity">
        update device
        <set>
            <if test="mac != null and mac != '' ">mac = #{mac},</if>
            <if test="serialPort != null and serialPort != '' ">serial_port = #{serialPort},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="type != null ">type = #{type},</if>
            <if test="cost != null ">cost = #{cost},</if>
            <if test="enable != null ">enable = #{enable},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="compartment != null ">compartment = #{compartment},</if>
            <if test="connectType != null ">connect_type = #{connectType},</if>
            <if test="transferStatus != null ">transfer_status = #{transferStatus},</if>
            <if test="baudRate != null ">baud_rate=#{baudRate},</if>
            <if test="dataBits != null ">data_bits=#{dataBits},</if>
            <if test="stopBits != null">stop_bits=#{stopBits},</if>
            <if test="parity != null">parity=#{parity},</if>
            <if test="connectStatus != null ">connect_status = #{connectStatus},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteDeviceById" parameterType="Long">
        delete from device where id = #{id}
    </delete>

    <delete id="deleteDeviceByIds" parameterType="Long">
        delete from device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
