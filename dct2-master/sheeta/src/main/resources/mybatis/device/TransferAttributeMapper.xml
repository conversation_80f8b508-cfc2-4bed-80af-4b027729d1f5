<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.device.mapper.TransferAttributeMapper">

    <resultMap type="TransferAttributeEntity" id="TransferAttributeResult">
        <id property="id" column="id"/>
        <result property="deviceCode" column="device_code"/>
        <result property="name" column="name"/>
        <result property="deviceName" column="device_name"/>
        <result property="label" column="label"/>
        <result property="orderNum" column="order_num"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="cost" column="cost"/>
        <result property="compartment" column="compartment"/>
    </resultMap>

    <sql id="selectTransferAttributeVo">
        select t.id, t.device_code, t.name,t.label, t.order_num, t.create_by, t.create_time, t.update_by, t.update_time,d.name as device_name,d.cost,d.compartment
		from transfer_attribute t LEFT JOIN device d on t.device_code = d.code
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="deviceCode !=null">
                and t.device_code = #{deviceCode}
            </if>
            <if test="name !=null and name != ''">
                and t.name = #{name}
            </if>
        </where>
    </sql>

    <select id="selectTransferAttribute" parameterType="TransferAttributeEntity" resultMap="TransferAttributeResult">
        <include refid="selectTransferAttributeVo"/>
        <include refid="sqlwhereSearch"/>
    </select>

    <select id="selectTransferAttributeList" parameterType="TransferAttributeEntity" resultMap="TransferAttributeResult">
        <include refid="selectTransferAttributeVo"/>
        <where>
            <if test="deviceCode !=null and deviceCode != ''">
                and t.device_code = #{deviceCode}
            </if>
            <if test="name != null and name != ''">
                AND t.name like concat('%', #{name}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(t.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(t.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <insert id="addTransferAttribute" parameterType="TransferAttributeEntity">
        insert into transfer_attribute (
        <if test="deviceCode != null and deviceCode != '' ">device_code,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="label != null and label != '' ">label,</if>
        <if test="orderNum != null and orderNum != '' ">order_num,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="deviceCode != null and deviceCode != '' ">#{deviceCode},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="label != null and label != ''">#{label},</if>
        <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateTransferAttribute" parameterType="TransferAttributeEntity">
        update transfer_attribute
        <set>
            <if test="deviceCode != null and deviceCode != '' ">device_code = #{deviceCode},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="label != null and label != ''">label = #{label},</if>
            <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteTransferAttributeById" parameterType="Long">
        delete from transfer_attribute where id = #{id}
    </delete>

    <delete id="deleteTransferAttributeByIds" parameterType="Long">
        delete from transfer_attribute where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTransferAttributeByCode" parameterType="String">
        delete from transfer_attribute where device_code = #{deviceCode}
    </delete>

</mapper>
