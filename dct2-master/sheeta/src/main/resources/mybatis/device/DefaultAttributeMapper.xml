<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.DefaultAttributeMapper">

    <resultMap type="DefaultAttribute" id="DefaultAttributeResult">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
    </resultMap>

    <sql id="selectDefaultAttributeVo">
        select id, type, name
		from default_attribute
    </sql>

    <select id="selectDefaultAttributeList" parameterType="DefaultAttribute" resultMap="DefaultAttributeResult">
        <include refid="selectDefaultAttributeVo"/>
        <where>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
        </where>
    </select>

</mapper>