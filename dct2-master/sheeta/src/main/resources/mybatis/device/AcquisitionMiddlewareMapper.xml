<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.AcquisitionMiddlewareMapper">

    <resultMap type="AcquisitionMiddleware" id="AcquisitionMiddlewareResult">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="mac" column="mac"/>
        <result property="ip" column="ip"/>
        <result property="port" column="port"/>
        <result property="connectStatus" column="connect_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="moduleModel" column="module_model"/>
        <result property="code" column="code"/>
    </resultMap>

    <sql id="selectAcquisitionMiddlewareVo">
        select id, type, name, mac, ip, port, connect_status, create_time, update_time,module_model,code
		from acquisition_middleware
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="type !=null">
                and type = #{type}
            </if>
            <if test="ip !=null and ip != ''">
                and ip = #{ip}
            </if>
            <if test="port !=null and port != ''">
                and port = #{port}
            </if>
            <if test="mac !=null and mac != ''">
                and mac = #{mac}
            </if>
        </where>
    </sql>

    <select id="selectAcquisitionMiddleware" parameterType="AcquisitionMiddleware" resultMap="AcquisitionMiddlewareResult">
        <include refid="selectAcquisitionMiddlewareVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectAcquisitionMiddlewareList" parameterType="AcquisitionMiddleware" resultMap="AcquisitionMiddlewareResult">
        <include refid="selectAcquisitionMiddlewareVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="type !=null">
                and type = #{type}
            </if>
            <if test="ip != null and ip != ''">
                AND ip like concat('%', #{ip}, '%')
            </if>
            <if test="port !=null and port != ''">
                AND port = #{port}
            </if>
            <if test="connectStatus !=null ">
                AND connect_status = #{connectStatus}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        order by connect_status
    </select>

    <insert id="addAcquisitionMiddleware" parameterType="AcquisitionMiddleware" useGeneratedKeys="true" keyProperty="id">
        insert into acquisition_middleware (
        <if test="type != null and type != '' ">type,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="mac != null and mac != '' ">mac,</if>
        <if test="ip != null and ip != '' ">ip,</if>
        <if test="port != null and port != '' ">port,</if>
        <if test="connectStatus != null">connect_status,</if>
        <if test="code != null and code != ''">code,</if>
        create_time
        )values(
        <if test="type != null and type != ''">#{type},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="mac != null and mac != ''">#{mac},</if>
        <if test="ip != null and ip != ''">#{ip},</if>
        <if test="port != null and port != ''">#{port},</if>
        <if test="connectStatus != null ">#{connectStatus},</if>
        <if test="code != null and code != ''">#{code},</if>
        sysdate()
        )
    </insert>

    <update id="updateAcquisitionMiddleware" parameterType="AcquisitionMiddleware">
        update acquisition_middleware
        <set>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="mac != null and mac != ''">mac = #{mac},</if>
            <if test="ip != null and ip != ''">ip = #{ip},</if>
            <if test="port != null and port != ''">port = #{port},</if>
            <if test="connectStatus != null ">connect_status = #{connectStatus},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <update id="connectStatusInvalid" >
        update acquisition_middleware set connect_status = 0
    </update>

    <delete id="deleteAcquisitionMiddlewareById" parameterType="Long">
        delete from acquisition_middleware where id = #{id}
    </delete>

    <delete id="deleteAcquisitionMiddlewareByMac" parameterType="String">
        delete from acquisition_middleware where mac = #{mac}
    </delete>

    <delete id="deleteByIpAndPort" parameterType="AcquisitionMiddleware">
        delete from acquisition_middleware where ip = #{ip} and port = #{port}
    </delete>

    <delete id="delete4Invalid" >
        delete from acquisition_middleware a where a.type is null and a.mac is null
    </delete>

    <delete id="deleteAcquisitionMiddlewareByIds" parameterType="Long">
        delete from acquisition_middleware where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
