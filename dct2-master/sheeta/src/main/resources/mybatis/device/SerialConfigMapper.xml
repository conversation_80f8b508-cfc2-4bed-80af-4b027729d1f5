<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.SerialConfigMapper">

    <resultMap type="SerialConfig" id="SerialConfigResult">
        <id property="id" column="id"/>
        <result property="oldName" column="old_name"/>
        <result property="newName" column="new_name"/>
        <result property="enable" column="enable"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSerialConfigVo">
        select id, old_name, new_name, enable, remark, create_by, update_by, create_time,update_time
		from serial_config
    </sql>

    <select id="selectSerialConfigList" parameterType="SerialConfig" resultMap="SerialConfigResult">
        <include refid="selectSerialConfigVo"/>
        <where>
            <if test="oldName != null and oldName != ''">
                AND old_name like concat('%', #{oldName}, '%')
            </if>
            <if test="newName != null and newName != ''">
                AND new_name like concat('%', #{newName}, '%')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectSerialConfig" parameterType="SerialConfig" resultMap="SerialConfigResult">
        <include refid="selectSerialConfigVo"/>
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
            <if test="oldName != null and oldName != ''">
                AND old_name = #{oldName}
            </if>
            <if test="newName != null and newName != ''">
                AND new_name = #{newName}
            </if>
        </where>
    </select>

    <insert id="addSerialConfig" parameterType="SerialConfig">
        insert into serial_config (
        <if test="newName != null and newName != '' ">new_name,</if>
        <if test="oldName != null and oldName != '' ">old_name,</if>
        <if test="remark != null and remark != '' ">remark,</if>
        <if test="enable != null ">enable,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="newName != null and newName != '' ">#{newName},</if>
        <if test="oldName != null and oldName != ''">#{oldName},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="enable != null ">#{enable},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateSerialConfig" parameterType="SerialConfig">
        update serial_config
        <set>
            <if test="newName != null and newName != '' ">new_name= #{newName},</if>
            <if test="oldName != null and oldName != '' ">old_name= #{oldName},</if>
            <if test="remark != null and remark != '' ">remark= #{remark},</if>
            <if test="enable != null ">enable= #{enable},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteSerialConfigById" parameterType="Long">
        delete from serial_config where id = #{id}
    </delete>

    <delete id="deleteSerialConfigByIds" parameterType="Long">
        delete from serial_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>