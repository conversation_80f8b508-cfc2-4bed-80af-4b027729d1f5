<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.ConnectLogMapper">

    <resultMap type="ConnectLog" id="ConnectLogResult">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="mac" column="mac"/>
        <result property="ip" column="ip"/>
        <result property="port" column="port"/>
        <result property="action" column="action"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectConnectLogVo">
        select id, type, name, mac, ip, port, action, create_time
		from connect_log
    </sql>

    <select id="selectConnectLogList" parameterType="ConnectLog" resultMap="ConnectLogResult">
        <include refid="selectConnectLogVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="mac != null and mac != ''">
                AND mac like concat('%', #{mac}, '%')
            </if>
            <if test="ip != null and ip != ''">
                AND ip like concat('%', #{ip}, '%')
            </if>
            <if test="port !=null and port != ''">
                AND port = #{port}
            </if>
            <if test="action !=null ">
                AND action = #{action}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>

    <insert id="addConnectLog" parameterType="ConnectLog">
        insert into connect_log (
        <if test="type != null ">type,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="mac != null and mac != '' ">mac,</if>
        <if test="ip != null and ip != '' ">ip,</if>
        <if test="port != null ">port,</if>
        <if test="action != null">action,</if>
        create_time
        )values(
        <if test="type != null ">#{type},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="mac != null and mac != ''">#{mac},</if>
        <if test="ip != null and ip != ''">#{ip},</if>
        <if test="port != null ">#{port},</if>
        <if test="action != null ">#{action},</if>
        sysdate()
        )
    </insert>

    <delete id="deleteConnectLogById" parameterType="Long">
        delete from connect_log where id = #{id}
    </delete>

    <delete id="deleteConnectLogByIds" parameterType="Long">
        delete from connect_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>