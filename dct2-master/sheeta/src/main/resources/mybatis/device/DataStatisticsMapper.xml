<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.DataStatisticsMapper">

    <resultMap type="DataStatistics" id="DataStatisticsResult">
        <id property="id" column="id"/>
        <result property="characterLength" column="character_length"/>
        <result property="deviceType" column="device_type"/>
        <result property="dataType" column="data_type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectDataStatisticsVo">
        select id, character_length, device_type, data_type,create_time
		from data_statistics
    </sql>

    <select id="selectDataStatisticsList" parameterType="DataStatistics" resultMap="DataStatisticsResult">
        <include refid="selectDataStatisticsVo"/>
        <where>
            <if test="deviceType != null">
                AND device_type = #{deviceType}
            </if>
            <if test="dataType != null">
                and data_type = #{dataType}
            </if>
        </where>
        order by create_time desc
    </select>

    <insert id="addDataStatistics" parameterType="DataStatistics">
        insert into data_statistics (
        <if test="characterLength != null ">character_length,</if>
        <if test="deviceType != null ">device_type,</if>
        <if test="dataType != null">data_type,</if>
        <if test="createTime != null">create_time</if>
        )values(
        <if test="characterLength != null ">#{characterLength},</if>
        <if test="deviceType != null">#{deviceType},</if>
        <if test="dataType != null">#{dataType},</if>
        <if test="createTime != null">#{createTime}</if>
        )
    </insert>

    <delete id="deleteDataStatisticsById" parameterType="Long">
        delete from data_statistics where id = #{id}
    </delete>

    <delete id="deleteDataStatisticsByIds" parameterType="Long">
        delete from data_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDataStatisticsByTime" parameterType="Long">
        delete from data_statistics where 1=1
        <![CDATA[and UNIX_TIMESTAMP(create_time)*1000 <= #{time}]]>
    </delete>
</mapper>