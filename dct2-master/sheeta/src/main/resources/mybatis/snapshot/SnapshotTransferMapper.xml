<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.snapshot.mapper.SnapshotTransferMapper">

    <resultMap type="SnapshotTransferEntity" id="SnapshotTransferResult">
        <id property="id" column="id"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="resolvingPower" column="resolving_power"/>
        <result property="compartment" column="compartment"/>
        <result property="cost" column="cost"/>
        <result property="status" column="status"/>
        <result property="operateTime" column="operate_time"/>
        <result property="directory" column="directory"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="fileName" column="file_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSnapshotTransferVo">
        select DISTINCT
            t.id,
            t.channel_code,
            sc.name channel_name,
            t.resolving_power,
            t.compartment,
            t.cost,
            t.status,
            t.transfer_status,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            l.operate_time,
            l.directory,
            l.file_name
        from snapshot_transfer t
            left join snapshot_channel sc on t.channel_code = sc.code
            left join snapshot_log l on sc.code = l.channel_code
    </sql>

    <select id="selectSnapshotTransfer" parameterType="SnapshotTransferEntity" resultMap="SnapshotTransferResult">
        <include refid="selectSnapshotTransferVo"/>
        <where>
            ((l.operate_time, t.channel_code) in (select
            max(operate_time),
            channel_code
            from snapshot_log
            GROUP BY channel_code) OR l.channel_code IS NULL
            )
            <if test="id !=null">
                and t.id = #{id}
            </if>
            <if test="channelCode !=null and channelCode != ''">
                and t.channel_code = #{channelCode}
            </if>
        </where>
    </select>


    <select id="selectSnapshotTransferList" parameterType="SnapshotTransferEntity" resultMap="SnapshotTransferResult">
        <include refid="selectSnapshotTransferVo"/>
        <where>
            ((l.operate_time, t.channel_code) in (select
            max(operate_time),
            channel_code
            from snapshot_log
            GROUP BY channel_code) OR l.channel_code IS NULL
            )
            <if test="channelCode !=null and channelCode != ''">
                and t.channel_code = #{channelCode}
            </if>
            <if test="cost !=null">
                and t.cost = #{cost}
            </if>
            <if test="status !=null">
                and t.status = #{status}
            </if>
        </where>
    </select>

    <insert id="addSnapshotTransfer" parameterType="SnapshotTransferEntity">
        insert into snapshot_transfer (
        <if test="channelCode != null and channelCode != ''">channel_code,</if>
        <if test="resolvingPower != null and resolvingPower != '' ">resolving_power,</if>
        <if test="compartment != null ">compartment,</if>
        <if test="cost != null  ">cost,</if>
        <if test="status != null ">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="transferStatus != null ">transfer_status,</if>
        create_time
        )values(
        <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
        <if test="resolvingPower != null and resolvingPower != '' ">#{resolvingPower},</if>
        <if test="compartment != null ">#{compartment},</if>
        <if test="cost != null ">#{cost},</if>
        <if test="status != null ">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="transferStatus != null ">#{transferStatus},</if>
        sysdate()
        )
    </insert>

    <update id="updateSnapshotTransfer" parameterType="SnapshotTransferEntity">
        update snapshot_transfer
        <set>
            <if test="resolvingPower != null and resolvingPower != ''">resolving_power = #{resolvingPower},</if>
            <if test="compartment != null ">compartment = #{compartment},</if>
            <if test="cost != null ">cost = #{cost},</if>
            <if test="status != null ">status = #{status},</if>
            <if test="transferStatus != null ">transfer_status = #{transferStatus},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteSnapshotTransferById" parameterType="Long">
        delete from snapshot_transfer where id = #{id}
    </delete>

    <delete id="deleteSnapshotTransferByIds" parameterType="Long">
        delete from snapshot_transfer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>