<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.snapshot.mapper.SnapshotChannelMapper">

    <resultMap type="SnapshotChannelEntity" id="SnapshotChannelResult">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="address" column="address"/>
        <result property="operateTime" column="operate_time"/>
        <result property="directory" column="directory"/>
        <result property="resolvingPower" column="resolving_power"/>
        <result property="compartment" column="compartment"/>
        <result property="cost" column="cost"/>
        <result property="trStatus" column="tr_status"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="fileName" column="file_name"/>
        <result property="storageTime" column="storage_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSnapshotChannelVo">
        select
            distinct
            c.id               as id,
            c.code             as code,
            c.name             as name,
            c.address          as address,
            /*快照时间*/
            l.operate_time     as operate_time,
            /*快照图片路径*/
            l.directory        as directory,
            /*快照存储状态*/
            tr.status          as tr_status,
            /*快照传输状态*/
            tr.transfer_status as transfer_status,
            /*分辨率*/
            tr.resolving_power as resolving_power,
            /*传输间隔*/
            tr.compartment     as compartment,
            /*优先级*/
            tr.cost            as cost,
            /*快照图片名称*/
            l.file_name        as file_name,
            c.storage_time     as storage_time,
            c.create_by        as create_by,
            c.create_time      as create_time,
            c.update_by        as update_by,
            c.update_time      as update_time
        from snapshot_channel c
            left join snapshot_log l on c.code = l.channel_code
            LEFT JOIN snapshot_transfer tr on c.`code` = tr.channel_code
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            ((l.operate_time,c.code) in(select max(operate_time),channel_code from snapshot_log GROUP BY channel_code) or l.channel_code is null)
            <if test="id != null">
                and c.id = #{id}
            </if>
            <if test="code !=null and code != ''">
                and c.code = #{code}
            </if>
            <if test="name !=null and name != ''">
                and c.name = #{name}
            </if>
        </where>
    </sql>

    <select id="selectSnapshotChannel" parameterType="SnapshotChannelEntity" resultMap="SnapshotChannelResult">
        <include refid="selectSnapshotChannelVo"/>
        <include refid="sqlwhereSearch"/>
    </select>


    <select id="selectSnapshotChannelList" parameterType="SnapshotChannelEntity" resultMap="SnapshotChannelResult">
        <include refid="selectSnapshotChannelVo"/>
        <where>
            ((l.operate_time,c.code) in(select max(operate_time),channel_code from snapshot_log GROUP BY channel_code) or l.channel_code is null)
            <if test="code !=null and code != ''">
                and  c.code = #{code}
            </if>
            <if test="name != null and name != ''">
                AND c.name like concat('%', #{name}, '%')
            </if>
            <if test="address != null and address != ''">
                AND c.address like concat('%', #{address}, '%')
            </if>
        </where>
    </select>

    <insert id="addSnapshotChannel" parameterType="SnapshotChannelEntity">
        insert into snapshot_channel (
        <if test="code != null and code != ''">code,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="address != null and address != '' ">address,</if>
        <if test="storageTime != null ">storage_time,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="code != null and code != ''">#{code},</if>
        <if test="name != null and name != '' ">#{name},</if>
        <if test="address != null and address != '' ">#{address},</if>
        <if test="storageTime != null ">#{storageTime},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>
    <update id="updateSnapshotChannel" parameterType="SnapshotChannelEntity">
        update snapshot_channel
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="storageTime != null ">storage_time = #{storageTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteSnapshotChannelById" parameterType="Long">
        delete from snapshot_channel where id = #{id}
    </delete>

    <delete id="deleteSnapshotChannelByIds" parameterType="Long">
        delete from snapshot_channel where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSnapshotChannelByCode" parameterType="String">
        delete from snapshot_channel where code = #{code}
    </delete>

</mapper>
