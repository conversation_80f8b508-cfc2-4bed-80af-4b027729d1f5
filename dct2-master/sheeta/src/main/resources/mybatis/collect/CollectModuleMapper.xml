<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.collect.mapper.CollectModuleMapper">

    <resultMap type="CollectModule" id="CollectModuleResult">
        <id property="id" column="id"/>
        <result property="model" column="model"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>

        <result property="nodeCode" column="node_code"/>
        <result property="nodeNum" column="node_num"/>

        <result property="baudRate" column="baud_rate"/>
        <result property="dataBits" column="data_bits"/>
        <result property="stopBits" column="stop_bits"/>
        <result property="parity" column="parity"/>
        <result property="shipIp" column="ship_ip"/>

        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCollectModuleVo">
        select id, model, name, code, node_code, node_num,
        baud_rate, data_bits, stop_bits, parity, ship_ip,
        create_by, create_time, update_by, update_time
		from collect_module
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
            <if test="code !=null and code != ''">
                and code = #{code}
            </if>
        </where>
    </sql>


    <select id="selectCollectModule" parameterType="CollectModule" resultMap="CollectModuleResult">
        <include refid="selectCollectModuleVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectCollectModuleList" parameterType="CollectModule" resultMap="CollectModuleResult">
        <include refid="selectCollectModuleVo"/>
        <where>
            <if test="model !=null and model != ''">
                and model = #{model}
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="code !=null and code != ''">
                and code = #{code}
            </if>
            <if test="nodeCode !=null and nodeCode != ''">
                and node_code = #{nodeCode}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <insert id="addCollectModule" parameterType="CollectModule">
        insert into collect_module (
        <if test="model != null and model != ''">model,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="code != null and code != '' ">code,</if>
        <if test="nodeCode != null and nodeCode != '' ">node_code,</if>
        <if test="nodeNum != null ">node_num,</if>
        <if test="baudRate != null ">baud_rate,</if>
        <if test="dataBits != null ">data_bits,</if>
        <if test="stopBits != null">stop_bits,</if>
        <if test="parity != null">parity,</if>
        <if test="shipIp != null and shipIp != '' ">ship_ip,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="model != null and model != ''">#{model},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="code != null and code != ''">#{code},</if>
        <if test="nodeCode != null and nodeCode != ''">#{nodeCode},</if>
        <if test="nodeNum != null">#{nodeNum},</if>
        <if test="baudRate != null">#{baudRate},</if>
        <if test="dataBits != null">#{dataBits},</if>
        <if test="stopBits != null">#{stopBits},</if>
        <if test="parity != null">#{parity},</if>
        <if test="shipIp != null and shipIp != ''">#{shipIp},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateCollectModule" parameterType="CollectModule">
        update collect_module
        <set>
            <if test="model != null and model != ''">model = #{model},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="nodeCode != null and nodeCode != ''">node_code = #{nodeCode},</if>
            <if test="nodeNum != null ">node_num = #{nodeNum},</if>
            <if test="baudRate != null ">baud_rate = #{baudRate},</if>
            <if test="dataBits != null ">data_bits = #{dataBits},</if>
            <if test="stopBits != null ">stop_bits = #{stopBits},</if>
            <if test="parity != null ">parity = #{parity},</if>
            <if test="shipIp != null and shipIp != ''">ship_ip = #{shipIp},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteCollectModuleById" parameterType="Long">
        delete from collect_module where id = #{id}
    </delete>

    <delete id="deleteCollectModuleByIds" parameterType="Long">
        delete from collect_module where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
