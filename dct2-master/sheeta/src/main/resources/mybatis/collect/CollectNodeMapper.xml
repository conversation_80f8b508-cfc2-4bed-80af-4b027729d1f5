<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.collect.mapper.CollectNodeMapper">

    <resultMap type="CollectNode" id="CollectNodeResult">
        <id property="id" column="id"/>
        <result property="model" column="model"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>

        <result property="netDhcp" column="net_dhcp"/>
        <result property="netIp" column="net_ip"/>
        <result property="netSubnetMask" column="net_subnet_mask"/>
        <result property="netGateway" column="net_gateway"/>

        <result property="wifiSsid" column="wifi_ssid"/>
        <result property="wifiPassword" column="wifi_password"/>
        <result property="wifiEncrypt" column="wifi_encrypt"/>

        <result property="isCollect" column="is_collect"/>
        <result property="baudRate" column="baud_rate"/>
        <result property="dataBits" column="data_bits"/>
        <result property="stopBits" column="stop_bits"/>
        <result property="parity" column="parity"/>
        <result property="shipIp" column="ship_ip"/>

        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="moduleName" column="module_name"/>
    </resultMap>

    <sql id="selectCollectNodeVo">
        select id, model, name, code, net_dhcp, net_ip, net_subnet_mask, net_gateway,
        wifi_ssid, wifi_password, wifi_encrypt,
        is_collect, baud_rate, data_bits, stop_bits, parity, ship_ip,
        create_by, create_time, update_by, update_time,module_name
		from collect_node
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
            <if test="code !=null and code != ''">
                and code = #{code}
            </if>
        </where>
    </sql>


    <select id="selectCollectNode" parameterType="CollectNode" resultMap="CollectNodeResult">
        <include refid="selectCollectNodeVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectCollectNodeList" parameterType="CollectNode" resultMap="CollectNodeResult">
        <include refid="selectCollectNodeVo"/>
        <where>
            <if test="model !=null and model != ''">
                and model = #{model}
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="code !=null and code != ''">
                and code = #{code}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <insert id="addCollectNode" parameterType="CollectNode">
        insert into collect_node (
        <if test="model != null and model != ''">model,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="code != null and code != '' ">code,</if>
        <if test="netDhcp != null ">net_dhcp,</if>
        <if test="netIp != null and netIp != '' ">net_ip,</if>
        <if test="netSubnetMask != null and netSubnetMask != '' ">net_subnet_mask,</if>
        <if test="netGateway != null and netGateway != '' ">net_gateway,</if>
        <if test="wifiSsid != null and wifiSsid != '' ">wifi_ssid,</if>
        <if test="wifiPassword != null and wifiPassword != '' ">wifi_password,</if>
        <if test="wifiEncrypt != null and wifiEncrypt != '' ">wifi_encrypt,</if>
        <if test="isCollect != null ">is_collect,</if>
        <if test="baudRate != null ">baud_rate,</if>
        <if test="dataBits != null ">data_bits,</if>
        <if test="stopBits != null">stop_bits,</if>
        <if test="parity != null">parity,</if>
        <if test="shipIp != null and shipIp != '' ">ship_ip,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="moduleName != null and moduleName != ''">module_name,</if>
        create_time
        )values(
        <if test="model != null and model != ''">#{model},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="code != null and code != ''">#{code},</if>
        <if test="netDhcp != null">#{netDhcp},</if>
        <if test="netIp != null and netIp != ''">#{netIp},</if>
        <if test="netSubnetMask != null and netSubnetMask != ''">#{netSubnetMask},</if>
        <if test="netGateway != null and netGateway != ''">#{netGateway},</if>
        <if test="wifiSsid != null and wifiSsid != ''">#{wifiSsid},</if>
        <if test="wifiPassword != null and wifiPassword != ''">#{wifiPassword},</if>
        <if test="wifiEncrypt != null and wifiEncrypt != ''">#{wifiEncrypt},</if>
        <if test="isCollect != null">#{isCollect},</if>
        <if test="baudRate != null">#{baudRate},</if>
        <if test="dataBits != null">#{dataBits},</if>
        <if test="stopBits != null">#{stopBits},</if>
        <if test="parity != null">#{parity},</if>
        <if test="shipIp != null and shipIp != ''">#{shipIp},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
        sysdate()
        )
    </insert>

    <update id="updateCollectNode" parameterType="CollectNode">
        update collect_node
        <set>
            <if test="model != null and model != ''">model = #{model},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="netDhcp != null ">net_dhcp = #{netDhcp},</if>
            <if test="netIp != null and netIp != ''">net_ip = #{netIp},</if>
            <if test="netSubnetMask != null and netSubnetMask != ''">net_subnet_mask = #{netSubnetMask},</if>
            <if test="netGateway != null and netGateway != ''">net_gateway = #{netGateway},</if>
            <if test="wifiSsid != null and wifiSsid != ''">wifi_ssid = #{wifiSsid},</if>
            <if test="wifiPassword != null and wifiPassword != ''">wifi_password = #{wifiPassword},</if>
            <if test="wifiEncrypt != null and wifiEncrypt != ''">wifi_encrypt = #{wifiEncrypt},</if>
            <if test="isCollect != null ">is_collect = #{isCollect},</if>
            <if test="baudRate != null ">baud_rate = #{baudRate},</if>
            <if test="dataBits != null ">data_bits = #{dataBits},</if>
            <if test="stopBits != null ">stop_bits = #{stopBits},</if>
            <if test="parity != null ">parity = #{parity},</if>
            <if test="shipIp != null and shipIp != ''">ship_ip = #{shipIp},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteCollectNodeById" parameterType="Long">
        delete from collect_node where id = #{id}
    </delete>

    <delete id="deleteCollectNodeByIds" parameterType="Long">
        delete from collect_node where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
