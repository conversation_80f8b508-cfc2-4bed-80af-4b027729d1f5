<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.index.mapper.SysRunStatusMapper">
	<resultMap type="SysOperLog2" id="SysOperLogResult">
		<result property="title"      		column="title"         	/>
		<result property="operTime"     	column="oper_time" 		/>
	</resultMap>

	<select id="sysRunStatus" resultMap="SysOperLogResult">
		select oper_time,title
		from sys_oper_log
		where left(title, 2)='设备' or left(title,2)='快照' or left(title,2)='通道'
		order by oper_time desc
		limit 20
	</select>
</mapper>
