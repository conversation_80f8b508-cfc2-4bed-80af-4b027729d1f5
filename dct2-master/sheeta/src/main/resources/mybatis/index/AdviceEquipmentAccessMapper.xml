<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.index.mapper.AdviceEquipmentAccessMapper">
	<resultMap type="SnapshotChannelEntity" id="SnapshotChannelEntity">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="address" column="address"/>
        <result property="operateTime" column="operate_time"/>
        <result property="directory" column="directory"/>
        <result property="resolvingPower" column="resolving_power"/>
        <result property="compartment" column="compartment"/>
        <result property="cost" column="cost"/>
        <result property="trStatus" column="tr_status"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="fileName" column="file_name"/>
        <result property="storageTime" column="storage_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
	</resultMap>

	<select id="seletctDeviceCount" resultType="integer">
		select count(d.id)
		from device d
		left join acquisition_middleware am on d.mac = am.mac
		where am.connect_status = 1
	</select>

	<select id="seletctDeviceAllCount" resultType="integer">
        select count(d.id)
		from device d
		left join acquisition_middleware am on d.mac = am.mac
    </select>

	<select id="selectDeviceAttributeCount" parameterType="integer" resultType="integer">
        select count(id)
		from device_attribute
		where type=#{type}
    </select>

	<select id="selectTransferAttributeCount"  resultType="integer">
         select count(t.id)
		from transfer_attribute t LEFT JOIN device d on t.device_code = d.code
    </select>

	<select id="seletctSnapshotAllCount" resultType="integer">
        select
            count(distinct(c.id))
        from snapshot_channel c
            left join snapshot_log l on c.code = l.channel_code
            LEFT JOIN snapshot_transfer tr on c.`code` = tr.channel_code
    </select>

	<select id="selectSnapshotChannelAllList"  resultMap="SnapshotChannelEntity">
		select
            distinct
            c.id               as id,
            c.code             as code,
            c.name             as name,
            c.address          as address,
            /*快照时间*/
            l.operate_time     as operate_time,
            /*快照图片路径*/
            l.directory        as directory,
            /*快照存储状态*/
            tr.status          as tr_status,
            /*快照传输状态*/
            tr.transfer_status as transfer_status,
            /*分辨率*/
            tr.resolving_power as resolving_power,
            /*传输间隔*/
            tr.compartment     as compartment,
            /*优先级*/
            tr.cost            as cost,
            /*快照图片名称*/
            l.file_name        as file_name,
            c.storage_time     as storage_time,
            c.create_by        as create_by,
            c.create_time      as create_time,
            c.update_by        as update_by,
            c.update_time      as update_time
        from snapshot_channel c
            left join snapshot_log l on c.code = l.channel_code
            LEFT JOIN snapshot_transfer tr on c.`code` = tr.channel_code
	</select>
    <select id="seletctDeviceAllCountList" resultType="Integer">
		select DISTINCT(type)
		from device
    </select>
</mapper>
