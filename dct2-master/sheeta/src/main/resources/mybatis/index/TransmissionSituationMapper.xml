<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.index.mapper.TransmissionSituationMapper">

    <resultMap type="DataList" id="DataList">
        <result property="createTime"      		column="create_time"         	/>
        <result property="characterLength"     	column="character_length" 		/>
    </resultMap>

	<select id="getAcquisition" parameterType="String" resultType="double">
		select ifnull(sum(character_length),0)
        from data_statistics
        where data_type=0
        <if test='_parameter=="1"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 HOUR) and '1'=#{type};
        </if>
        <if test='_parameter=="2"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 DAY) and '2'=#{type};
        </if>
        <if test='_parameter=="3"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 MONTH) and '3'=#{type};
        </if>
	</select>

    <select id="getAcacquisitionDevice" parameterType="String" resultType="double">
        select ifnull(sum(character_length),0)
        from data_statistics
        where data_type=0 and device_type=0
        <if test='_parameter=="1"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 HOUR) and '1'=#{type};
        </if>
        <if test='_parameter=="2"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 DAY) and '2'=#{type};
        </if>
        <if test='_parameter=="3"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 MONTH) and '3'=#{type};
        </if>
    </select>

    <select id="getAcquisitionSnapshot" parameterType="String" resultType="double">
        select ifnull(sum(character_length),0)
        from data_statistics
        where data_type=0 and device_type=1
        <if test='_parameter=="1"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 HOUR) and '1'=#{type};
        </if>
        <if test='_parameter=="2"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 DAY) and '2'=#{type};
        </if>
        <if test='_parameter=="3"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 MONTH) and '3'=#{type};
        </if>
    </select>

    <select id="getTransmission" parameterType="String" resultType="double">
        select ifnull(sum(character_length),0)
        from data_statistics
        where data_type=1
        <if test='_parameter=="1"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 HOUR) and '1'=#{type};
        </if>
        <if test='_parameter=="2"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 DAY) and '2'=#{type};
        </if>
        <if test='_parameter=="3"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 MONTH) and '3'=#{type};
        </if>
    </select>

    <select id="getTransmissionDevice" parameterType="String" resultType="double">
        select ifnull(sum(character_length),0)
        from data_statistics
        where data_type=1 and device_type=0
        <if test='_parameter=="1"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 HOUR) and '1'=#{type};
        </if>
        <if test='_parameter=="2"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 DAY) and '2'=#{type};
        </if>
        <if test='_parameter=="3"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 MONTH) and '3'=#{type};
        </if>
    </select>

    <select id="getTransmissionSnapshot" parameterType="String" resultType="double">
        select ifnull(sum(character_length),0)
        from data_statistics
        where data_type=1 and device_type=1
        <if test='_parameter=="1"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 HOUR) and '1'=#{type};
        </if>
        <if test='_parameter=="2"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 DAY) and '2'=#{type};
        </if>
        <if test='_parameter=="3"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 MONTH) and '3'=#{type};
        </if>
    </select>

    <select id="getDeviceDataList" parameterType="String" resultMap="DataList">
        select create_time,character_length
        from data_statistics
        where data_type=1 and device_type=0
        <if test='_parameter=="1"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 HOUR) and '1'=#{type};
        </if>
        <if test='_parameter=="2"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 DAY) and '2'=#{type};
        </if>
        <if test='_parameter=="3"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 MONTH) and '3'=#{type};
        </if>
     </select>

    <select id="getSnapshotDataList" parameterType="String" resultMap="DataList">
        select create_time,character_length
        from data_statistics
        where data_type=1 and device_type=1
        <if test='_parameter=="1"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 HOUR) and '1'=#{type};
        </if>
        <if test='_parameter=="2"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 DAY) and '2'=#{type};
        </if>
        <if test='_parameter=="3"'>
            and create_time>=DATE_SUB(NOW(),INTERVAL  1 MONTH) and '3'=#{type};
        </if>
     </select>
</mapper>
