# 项目相关配置
project:
  # 名称
  name: sheeta
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2020
  # 实例演示开关
  demoEnabled: false
  # 文件路径 示例（ Windows配置D:/xhjt/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: E:/xhjt/uploadPath
  # 获取ip地址开关
  addressEnabled: true
  # 源数据保存路径 示例（ Windows配置E:/xhjt/sourceData/，Linux配置 /home/<USER>/sourceData/）
  sourceDataPath: D:\CodeLib\dct2\dct2-master\sheeta\sourceData
  #  sourceDataPath: /home/<USER>/sourceData/
  snapshotPath: /home/<USER>/snapshot
  # ffmpeg软件的安装路径
  ffmpegPath: /usr/local/ffmpeg2/bin/ffmpeg


# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9991
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.xhjt: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
#  profiles:
#    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 密码
    password: 123456
    # 数据库
    database: 3
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  # kafka 配置
  kafka:
    bootstrap-servers: http://localhost:9092
    producer:
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
#        url: jdbc:mysql://**************:3306/dct?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
#        username: root
#        password: xhjt##6582850
#        url: jdbc:mysql://*************:3306/dct?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
#        username: root
#        password: xhjt##6582850
        url: ********************************************************************************************************************************************
        username: root
        password: 123456
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌秘钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.xhjt.project.**.domain,com.xhjt.dctcore.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mybatis/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

#Feign的请求Url设置
feign:
  duffyUrl: http://localhost:9998/duffy
  dolaUrl: http://localhost:9996
# 滑块验证码
aj:
  captcha:
    # blockPuzzle滑块 clickWord文字点选  default默认两者都实例化
    type: blockPuzzle
    # 滑动验证，底图路径，不配置将使用默认图片
    # 支持全路径
    # 支持项目路径,以classpath:开头,取resource目录下路径,例：classpath:images/jigsaw
    jigsaw: classpath:images/jigsaw
    pic-click: classpath:images/pic-click
    # 右下角显示字  https://tool.chinaz.com/tools/unicode.aspx 中文转Unicode
    water-mark: \u897f\u6d77\u4f73\u901a
    # 校验滑动拼图允许误差偏移量(默认5像素)
    slip-offset: 5
    # aes加密坐标开启或者禁用(true|false)
    aes-status: true
    # 滑动干扰项(0/1/2)
    interference-options: 2
    # 验证失败5次，get接口锁定
    req-get-lock-limit: 5
    # 验证失败后，锁定时间间隔,s
    req-get-lock-seconds: 300
    # get接口一分钟内请求数限制
    req-get-minute-limit: 30
    # check接口一分钟内请求数限制
    req-check-minute-limit: 30
