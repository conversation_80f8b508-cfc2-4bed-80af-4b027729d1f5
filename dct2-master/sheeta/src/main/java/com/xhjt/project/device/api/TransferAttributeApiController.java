package com.xhjt.project.device.api;

import com.alibaba.fastjson.JSON;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.project.device.domain.*;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.TransferAttributeService;
import com.xhjt.project.netty.common.AmUtil;
import com.xhjt.project.netty.common.DeviceUtil;
import com.xhjt.project.netty.common.RedisDeviceUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/transferAttribute")
public class TransferAttributeApiController extends BaseController {

    @Autowired
    private TransferAttributeService transferAttributeService;
    @Autowired
    private DeviceService deviceService;
    /**
     * 修改设备传输属性
     *
     * @param jsonObject
     * @param deviceCode
     */
    @PostMapping("/updateBySync")
    public void updateBySync(String jsonObject, String deviceCode) {
        List<TransferAttributeEntity> tranList = JSON.parseArray(jsonObject, TransferAttributeEntity.class);
        if (tranList.size() == 0) {
            return;
        }
        transferAttributeService.updateBySync(tranList, deviceCode);
    }

    /**
     * 修改设备传输属性-new
     *
     * @param jsonObject
     * @param deviceCode
     */
    @PostMapping("/updateTaAndDeviceBySync")
    public void updateTaAndDeviceBySync(String jsonObject, String deviceCode) {
        Transfer2DeviceVo transferDeviceVo = JSON.parseObject(jsonObject, Transfer2DeviceVo.class);
        if (transferDeviceVo == null) {
            return;
        }
        logger.info("------------"+transferDeviceVo.getDeviceStatus().toString());
        DeviceStatus device = transferDeviceVo.getDeviceStatus();
        DeviceEntity newDevice = deviceService.selectByCode(deviceCode);
        newDevice.setCompartment(device.getCompartment());
        deviceService.updateDevice(newDevice);
        //更新缓存
        DeviceUtil.renewDevice(newDevice);
        RedisDeviceUtil.renewDevice(newDevice);
        //更新传输属性集合
        List<Transfer2SheetaEntity> tranList = transferDeviceVo.getTransferAttributeEntities();
        List<TransferAttributeEntity> transferAttributeEntities = new ArrayList<>();
        for (Transfer2SheetaEntity transfer2SheetaEntity:
                tranList) {
            TransferAttributeEntity transferAttributeEntity = new TransferAttributeEntity();
            BeanUtils.copyProperties(transfer2SheetaEntity,transferAttributeEntity);
            transferAttributeEntities.add(transferAttributeEntity);
        }

        transferAttributeService.updateBySync(transferAttributeEntities, deviceCode);
        //更新缓存
        transferAttributeService.renewAttributes(newDevice.getCode());
     }

}
