package com.xhjt.project.device.service;

import com.xhjt.project.device.domain.DefaultAttribute;
import com.xhjt.project.device.mapper.DefaultAttributeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备默认属性
 *
 * <AUTHOR>
 */
@Service
public class DefaultAttributeService {

    @Autowired
    private DefaultAttributeMapper defaultAttributeMapper;


    /**
     * 设备默认属性
     *
     * @param defaultAttribute
     * @return 设备默认属性集合
     */
    public List<DefaultAttribute> selectDefaultAttributeList(DefaultAttribute defaultAttribute) {
        return defaultAttributeMapper.selectDefaultAttributeList(defaultAttribute);
    }
}
