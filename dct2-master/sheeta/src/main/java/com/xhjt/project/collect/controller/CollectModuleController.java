package com.xhjt.project.collect.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.collect.domain.CollectModule;
import com.xhjt.project.collect.domain.CollectNode;
import com.xhjt.project.collect.service.CollectModuleService;
import com.xhjt.project.collect.service.CollectNodeService;
import com.xhjt.project.collect.service.WriteCmdService;
import com.xhjt.project.index.service.IndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * 采集模块 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/collect/module")
public class CollectModuleController extends BaseController {

    @Autowired
    private CollectModuleService collectModuleService;
    @Autowired
    private WriteCmdService writeCmdService;
    @Autowired
    CollectNodeService collectNodeService;


    /**
     * 获取采集模块列表
     */
    @PreAuthorize("@ss.hasPermi('collect:module:list')")
    @GetMapping("/list")
    public TableDataInfo list(CollectModule collectModule) {
        startPage();
        List<CollectModule> list = collectModuleService.selectCollectModuleList(collectModule);
        list.stream().forEach(collectModule1 -> {
            List<CollectNode> collectNodes = collectNodeService.selectCollectNodeByCode(collectModule1.getNodeCode());
            if (collectNodes != null && collectNodes.size()>0){
                collectModule1.setNodeName(collectNodes.get(0).getName());
            }
        });
        return getDataTable(list);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('collect:module:query')")
    @GetMapping(value = "/{collectModuleId}")
    public AjaxResult getInfo(@PathVariable Long collectModuleId) {
        return AjaxResult.success(collectModuleService.selectCollectModuleById(collectModuleId));
    }

    /**
     * 获取当前登录地址的ip
     */
    @PreAuthorize("@ss.hasPermi('collect:module:query')")
    @GetMapping("/wanIp/getIp")
    public AjaxResult getWanIp() {
        return AjaxResult.success(getHostIp());
    }

    /**
     * 新增采集模块
     */
    @PreAuthorize("@ss.hasPermi('collect:module:add')")
    @Log(title = "采集模块新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CollectModule collectModule) throws Exception {
        collectModule.setCreateBy(SecurityUtils.getUsername());
        int row = 0;
        List<CollectNode> collectNodes = collectNodeService.selectCollectNodeByCode(collectModule.getNodeCode());
        if (collectNodes != null && collectNodes.size()>0){
            collectModuleService.addBefore(collectModule);
            //写入配置
            writeCmdService.writeConfig(collectModule,collectNodes.get(0));
            row = collectModuleService.addCollectModule(collectModule);
        }
        return toAjax(row);
    }

    /**
     * 修改采集模块
     */
    @PreAuthorize("@ss.hasPermi('collect:module:edit')")
    @Log(title = "采集模块修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody CollectModule collectModule) throws Exception {
        collectModule.setUpdateBy(SecurityUtils.getUsername());
        int row = collectModuleService.updateCollectModule(collectModule);
        List<CollectNode> collectNodes = collectNodeService.selectCollectNodeByCode(collectModule.getNodeCode());
        if (collectNodes != null && collectNodes.size()>0){
            //写入配置
            writeCmdService.writeConfig(collectModule,collectNodes.get(0));
        }
        return toAjax(row);
    }

    /**
     * 删除采集模块
     */
    @PreAuthorize("@ss.hasPermi('collect:module:remove')")
    @Log(title = "采集模块删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{collectModuleIds}")
    public AjaxResult remove(@PathVariable Long[] collectModuleIds) {
        return toAjax(collectModuleService.deleteCollectModuleByIds(collectModuleIds));
    }
    private static List<String> getHostIp() {
        List<String> ipv4Result = new ArrayList<>();
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
                Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress ip = (InetAddress) addresses.nextElement();
                    if (ip != null
                            && ip instanceof Inet4Address
                            && !ip.isLoopbackAddress() //loopback地址即本机地址，IPv4的loopback范围是********* ~ ***************
                            && ip.getHostAddress().indexOf(":") == -1) {
                        ipv4Result.add(ip.getHostAddress());
                     }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ipv4Result;


    }
}
