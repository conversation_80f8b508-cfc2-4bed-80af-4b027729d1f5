package com.xhjt.project.device.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.ClientListernerUtil;
import com.xhjt.common.SerialPortUtil;
import com.xhjt.common.exception.CustomException;
import com.xhjt.common.utils.ListUtil;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.device.DeviceAttributeEntity;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.framework.serialport.RtxtService;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.TransferDeviceVo;
import com.xhjt.project.device.mapper.DeviceMapper;
import com.xhjt.project.feign.DuffyApi;
import com.xhjt.project.monitor.domain.SysOperLog;
import com.xhjt.project.monitor.service.ISysOperLogService;
import com.xhjt.project.netty.common.AmUtil;
import com.xhjt.project.netty.common.DeviceUtil;
import com.xhjt.project.netty.common.RedisDeviceUtil;
import com.xhjt.project.netty.service.StoreService;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class DeviceService {
    @Autowired
    private DeviceMapper deviceMapper;

    private static String A2Z = "ABCDEFGHIJKLMNPQRSTUVWXYZ";

    @Autowired
    private StoreService storeService;

    @Autowired
    private DuffyApi duffyApi;
    @Autowired
    private TransferAttributeService transferAttributeService;

    @Autowired
    private DeviceAttributeService deviceAttributeService;

    @Autowired
    private ClientListernerUtil clientListernerUtil;

    @Autowired
    private RtxtService rtxtService;

    @Autowired
    private ISysOperLogService iSysOperLogService;
    @Autowired
    private AcquisitionMiddlewareService acquisitionMiddlewareService;

    /**
     * 查询设备信息
     *
     * @param id 设备ID
     * @return 设备信息
     */
    @Transactional(rollbackFor = Exception.class)
    public DeviceEntity selectDeviceById(Long id) {
        DeviceEntity device = new DeviceEntity();
        device.setId(id);
        return deviceMapper.selectDevice(device);
    }

    /**
     * 查询设备列表
     *
     * @param device 设备信息
     * @return 设备集合
     */
    @Transactional(rollbackFor = Exception.class)
    public List<DeviceEntity> selectDeviceList(DeviceEntity device) {
        return deviceMapper.selectDeviceList(device);
    }

    /**
     * 根据IP和Port查询设备信息
     *
     * @return 设备信息
     */
    @Transactional(rollbackFor = Exception.class)
    public DeviceEntity selectByMac(String mac) {
        DeviceEntity device = new DeviceEntity();
        device.setMac(mac);
        device.setConnectType(0);//采集终端
        return deviceMapper.selectDevice(device);
    }

    /**
     * 根据IP和Port查询设备信息
     *
     * @return 设备信息
     */
    @Transactional(rollbackFor = Exception.class)
    public DeviceEntity selectByCode(String code) {
        DeviceEntity device = new DeviceEntity();
        device.setCode(code);
        return deviceMapper.selectDevice(device);
    }

    /**
     * 新增设备
     *
     * @param device 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized DeviceEntity addDevice(DeviceEntity device) throws Exception {
        String mac = "";
        String msg = "";
        if (device.getConnectType()==1){
            mac = device.getSerialPort()+device.getCode();
            msg = "该串口已被绑定";
        }else {
            mac = device.getMac()+device.getCode();
            msg = "该模板已被绑定";
        }
        if (DeviceUtil.getDevice(mac) != null) {
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("设备'" + device.getName() + "'新增失败");
            iSysOperLogService.insertOperlog(sysOperLog);
            throw new CustomException("设备'" + device.getName() + "'新增失败，"+msg);
        }
        //生成code
        device.setCode(getCode(device.getType()));

        duffyApi.createTable(device.getType(), device.getCode());
        //默认开启存储以及传输状态
        device.setEnable(1);//存储
        device.setTransferStatus(1);//传输
        device.setConnectStatus(1);//第一次保存得时候连接成功
        //默认给优先级以及传输间隔-待确认
        int row = deviceMapper.addDevice(device);

        if (row > 0) {

//            am.setType(device.getType());
//            acquisitionMiddlewareService.updateAcquisitionMiddleware(am);

            //同步到岸端
//            acquisitionMiddlewareService.syncNewShore(am,2);
            //开启监听
            if (device.getConnectType()==1){
                this.serialPortView(device);
            }
            DeviceUtil.addAllDevice();
            RedisDeviceUtil.addAllDevice();
            //更新缓存
            if (device.getConnectType()==0){
                //添加设备得时候把type维护到采集终端
                AcquisitionMiddleware am = acquisitionMiddlewareService.selectAcquisitionMiddlewareByMac(device.getMac());
                am.setEnable(1);
                AmUtil.renewAmByDevice(am);
            }

        }

        return device;
    }

    /**
     * 获取编码，编码为类型加上大写字母，不够4位用0补齐
     *
     * @param type
     * @return
     * @throws Exception
     */
    private String getCode(Integer type) throws Exception {
        List<DeviceEntity> list = selectDeviceList(null);
        List<String> codeList = ListUtil.fetchFieldValuesList(list, "code");

        String code = "";

        for (int i = 0; i < A2Z.length(); i++) {
            String str = A2Z.substring(i, i + 1);
            code = type.toString() + str;
            if (code.length() < 4) {
                code = StringUtils.leftPad(code, 4, "0");
            }
            if (!codeList.contains(code)) {
                break;
            }
        }
        return code;
    }

    /**
     * 修改设备
     *
     * @param device 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateDevice(DeviceEntity device) {
        int row = deviceMapper.updateDevice(device);
        if (row>0){
            //更新缓存
            DeviceUtil.addAllDevice();
            RedisDeviceUtil.addAllDevice();
            AmUtil.renewAmByEnable(device);
        }
        return row;
    }

    /**
     * 删除设备信息
     *
     * @param id 参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteDeviceById(Long id) {
        DeviceEntity device = selectDeviceById(id);
        if (device == null) {
            return 0;
        }
        //关闭监听
        if (device.getConnectType() == 1){
            this.delSerialPortView(device.getSerialPort());
            DeviceUtil.removeDevice(device.getSerialPort());
        }else{
            DeviceUtil.removeDevice(device.getMac());
        }
        RedisDeviceUtil.removeDevice(device.getCode());
        return deviceMapper.deleteDeviceById(id);
    }

    /**
     * 批量删除参数信息
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteDeviceByIds(Long[] ids) {
        for (Long id : ids) {
            DeviceEntity device = selectDeviceById(id);
            if (device == null) {
                continue;
            }else{
                //同时删除设备相关属性信息
                transferAttributeService.deleteTransferAttributeByCode(device.getCode());
            }

            duffyApi.removeTable(device.getType(), device.getCode());
            if (device.getConnectType() == 1){
                this.delSerialPortView(device.getSerialPort());
                DeviceUtil.removeDevice(device.getSerialPort()+device.getCode());
            }else{
                DeviceUtil.removeDevice(device.getMac()+device.getCode());
            }

            RedisDeviceUtil.removeDevice(device.getCode());
            AmUtil.removeAm(device.getIp(),device.getPort());
        }

        return deviceMapper.deleteDeviceByIds(ids);
    }


    /**
     * 启用-存储
     *
     * @param id 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public DeviceEntity enable(Long id) {
        DeviceEntity deviceEntity = selectDeviceById(id);
        if (deviceEntity == null) {
            return null;
        }
        //开启监听
        if (StringUtils.isNotBlank(deviceEntity.getSerialPort())){
            this.serialPortView(deviceEntity);
        }
        if (deviceEntity.getEnable() == 1) {
            return deviceEntity;
        }
        deviceEntity.setEnable(1);
        updateDevice(deviceEntity);

        DeviceUtil.renewDevice(deviceEntity);
        RedisDeviceUtil.renewDevice(deviceEntity);
        AmUtil.renewAmByEnable(deviceEntity);

        return deviceEntity;
    }

    /**
     * 关闭-存储
     *
     * @param id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public DeviceEntity disable(Long id) {
        DeviceEntity deviceEntity = selectDeviceById(id);
        if (deviceEntity == null) {
            return null;
        }
        //关闭监听
        if (StringUtils.isNotBlank(deviceEntity.getSerialPort())){
            this.delSerialPortView(deviceEntity.getSerialPort());
        }
        if (deviceEntity.getEnable() == 0) {
            return deviceEntity;
        }
        deviceEntity.setEnable(0);
        updateDevice(deviceEntity);

        DeviceUtil.renewDevice(deviceEntity);
        RedisDeviceUtil.renewDevice(deviceEntity);
        AmUtil.renewAmByEnable(deviceEntity);

        return deviceEntity;
    }

    /**
     * 同步设备数据到岸上
     *
     * @param deviceEntity
     * @param action
     */
    public void sync2Shore(DeviceEntity deviceEntity, int action) {
        DeviceEntity entity = new DeviceEntity();
        entity.setType(deviceEntity.getType());
        entity.setCode(deviceEntity.getCode());
        entity.setName(deviceEntity.getName());
        entity.setEnable(deviceEntity.getEnable());

        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(entity), action, "device");

        KafkaMessage kafkaMessage = new KafkaMessage(deviceEntity.getCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 岸端设备状态修改调用
     * @param deviceEntity
     */
    public void syncByShore(DeviceEntity deviceEntity) {
        DeviceEntity entity = selectByCode(deviceEntity.getCode());
        entity.setEnable(deviceEntity.getEnable());//存储状态
        entity.setTransferStatus(deviceEntity.getTransferStatus());//传输状态
        updateDevice(entity);

        DeviceUtil.renewDevice(entity);
        RedisDeviceUtil.renewDevice(deviceEntity);
        AmUtil.renewAmByEnable(entity);
    }

    /**
     * 启用-传输
     *
     * @param id 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public DeviceEntity changeTrStatus(Long id,Integer status) {
        DeviceEntity deviceEntity = selectDeviceById(id);
        if (deviceEntity == null) {
            return null;
        }
        if (status==1){
            deviceEntity.setEnable(status);
        }
        deviceEntity.setTransferStatus(status);
        updateDevice(deviceEntity);

        DeviceUtil.renewDevice(deviceEntity);
        RedisDeviceUtil.renewDevice(deviceEntity);
        AmUtil.renewAmByEnable(deviceEntity);
        return deviceEntity;
    }

    /**
     * 开启监听
     * @param deviceEntity
     * @return
     */
    public int serialPortView(DeviceEntity deviceEntity) {
        return rtxtService.toListenerSerialPort(deviceEntity);
    }

    /**
     * 关闭临时监听
     * @param serialPort
     * @return
     */
    public void delSerialPortView(String serialPort) {
         SerialPortUtil.delOpenPort(serialPort);
    }

    public List<TableHead> getTableHead(Long deviceId) {
        DeviceEntity device = this.selectDeviceById(deviceId);

        List<TransferAttributeEntity> attributeList = transferAttributeService.selectListByCode(device.getCode());

        List<TableHead> list = Lists.newArrayList();
        addBjTime(list);

        for (TransferAttributeEntity entity : attributeList) {
            TableHead tableHead = new TableHead();
            tableHead.setLabel(entity.getLabel());
            tableHead.setProperty(entity.getName());

            list.add(tableHead);
        }

        return list;
    }

    private void addBjTime(List<TableHead> list){
        TableHead tableHead = new TableHead();
        tableHead.setLabel("采集时间");
        tableHead.setProperty("initialBjTime");

        list.add(tableHead);
    }

    public List<TableHead> getTableHeadByType(Integer type) {
        List<DeviceAttributeEntity> attributeList = deviceAttributeService.selectListByType(type);
        List<TableHead> list = Lists.newArrayList();
        addBjTime(list);
        for (DeviceAttributeEntity entity : attributeList) {
            TableHead tableHead = new TableHead();
            tableHead.setLabel(entity.getLabel());
            tableHead.setProperty(entity.getName());

            list.add(tableHead);
        }

        return list;
    }

    public class TableHead {
        /**
         * 文本
         */
        private String label;
        /**
         * 属性
         */
        private String property;

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getProperty() {
            return property;
        }

        public void setProperty(String property) {
            this.property = property;
        }
    }

    private static String bytesToTenString(byte[] byteArr) {
        StringBuilder sb = new StringBuilder(byteArr.length);
        String sTemp;
        for (byte b : byteArr) {
            sTemp = Integer.toHexString(0xFF & b);
            long decNum = Long.parseLong(sTemp, 16);
            sb.append(decNum).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 同步设备数据到岸上
     *
     * @param transferDeviceEntity
     * @param action
     */
    public void syncNewShore(TransferDeviceVo transferDeviceEntity, int action) {
        List<TransferAttributeEntity> sourceList = transferDeviceEntity.getTransferAttributeEntities();
        if (sourceList != null && sourceList.size()>0){
            List<TransferAttributeEntity> list = sourceList.stream().map(ta -> {
                TransferAttributeEntity entity = new TransferAttributeEntity();
                entity.setName(ta.getName());
                entity.setOrderNum(ta.getOrderNum());
                return entity;
            }).collect(Collectors.toList());
            transferDeviceEntity.setTransferAttributeEntities(list);
        }
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(transferDeviceEntity), action, "transferDevice");

        KafkaMessage kafkaMessage = new KafkaMessage(transferDeviceEntity.getDeviceEntity().getCode(), JSONObject.toJSONString(syncEntity), transferDeviceEntity.getDeviceEntity().getCost(), System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }
}
