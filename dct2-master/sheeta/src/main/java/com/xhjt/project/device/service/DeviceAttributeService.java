package com.xhjt.project.device.service;

import com.xhjt.dctcore.commoncore.domain.device.DeviceAttributeEntity;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.device.mapper.DeviceAttributeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class DeviceAttributeService {
    @Autowired
    private DeviceAttributeMapper deviceAttributeMapper;


    /**
     * 查询设备信息
     *
     * @param deviceAttributeId 设备ID
     * @return 设备信息
     */
    public DeviceAttributeEntity selectDeviceAttributeById(Long deviceAttributeId) {
        DeviceAttributeEntity deviceAttribute = new DeviceAttributeEntity();
        deviceAttribute.setId(deviceAttributeId);
        return deviceAttributeMapper.selectDeviceAttribute(deviceAttribute);
    }

    /**
     * 查询设备列表
     *
     * @param deviceAttribute 设备信息
     * @return 设备集合
     */
    public List<DeviceAttributeEntity> selectDeviceAttributeList(DeviceAttributeEntity deviceAttribute) {
        return deviceAttributeMapper.selectDeviceAttributeList(deviceAttribute);
    }

    /**
     * 查询设备列表
     *
     * @param type
     * @return 设备集合
     */
    public List<DeviceAttributeEntity> selectListByType(Integer type) {
        DeviceAttributeEntity deviceAttribute = new DeviceAttributeEntity();
        deviceAttribute.setType(type);
        return deviceAttributeMapper.selectDeviceAttributeList(deviceAttribute);
    }

    /**
     * 新增设备
     *
     * @param deviceAttribute 设备信息
     * @return 结果
     */
    public int addDeviceAttribute(DeviceAttributeEntity deviceAttribute) {
        return deviceAttributeMapper.addDeviceAttribute(deviceAttribute);
    }

    /**
     * 修改设备
     *
     * @param deviceAttribute 设备信息
     * @return 结果
     */
    public int updateDeviceAttribute(DeviceAttributeEntity deviceAttribute) {
        return deviceAttributeMapper.updateDeviceAttribute(deviceAttribute);
    }

    /**
     * 删除设备信息
     *
     * @param deviceAttributeId 参数ID
     * @return 结果
     */
    public int deleteDeviceAttributeById(Long deviceAttributeId) {
        return deviceAttributeMapper.deleteDeviceAttributeById(deviceAttributeId);
    }

    /**
     * 批量删除参数信息
     *
     * @param deviceAttributeIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteDeviceAttributeByIds(Long[] deviceAttributeIds) {
        return deviceAttributeMapper.deleteDeviceAttributeByIds(deviceAttributeIds);
    }

    /**
     * 通过类型获取对应的设备属性
     * @param type
     * @return
     */
    public List<DeviceAttributeEntity> selectListByType(String type) {
        DeviceAttributeEntity deviceAttributeEntity = new DeviceAttributeEntity();
        deviceAttributeEntity.setType(Integer.parseInt(type));
        return deviceAttributeMapper.selectDeviceAttributeList(deviceAttributeEntity);
    }

}
