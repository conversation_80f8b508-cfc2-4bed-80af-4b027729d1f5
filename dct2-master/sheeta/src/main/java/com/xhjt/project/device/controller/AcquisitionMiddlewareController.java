package com.xhjt.project.device.controller;

import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.collect.domain.CollectModule;
import com.xhjt.project.collect.domain.CollectNode;
import com.xhjt.project.collect.service.CollectModuleService;
import com.xhjt.project.collect.service.CollectNodeService;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.AcquisitionMiddlewareService;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.netty.common.AmUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/am")
public class AcquisitionMiddlewareController extends BaseController {

    @Autowired
    private AcquisitionMiddlewareService acquisitionMiddlewareService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private CollectNodeService collectNodeService;
    @Autowired
    private CollectModuleService collectModuleService;
    /**
     * 获取采集终端信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(AcquisitionMiddleware am) {
        startPage();
        List<AcquisitionMiddleware> list = acquisitionMiddlewareService.selectAcquisitionMiddlewareList(am);
        list.stream().forEach(acquisitionMiddleware -> {
            DeviceEntity device = new DeviceEntity();
            device.setMac(acquisitionMiddleware.getMac());
            device.setConnectType(0);//采集终端
            List<DeviceEntity> deviceEntity = deviceService.selectDeviceList(device);
            List<CollectModule> collectModules = collectModuleService.selectCollectModuleByCode(acquisitionMiddleware.getCode());
            if (collectModules != null && collectModules.size()>0){
                acquisitionMiddleware.setName(collectModules.get(0).getName());
                acquisitionMiddleware.setModuleModel(collectModules.get(0).getModel());
            }else {
                List<CollectNode> collectNodes = collectNodeService.selectCollectNodeByCode(acquisitionMiddleware.getCode());
                if (collectNodes != null && collectNodes.size()>0){
                    acquisitionMiddleware.setName(collectNodes.get(0).getModuleName());
                    acquisitionMiddleware.setModuleModel(collectNodes.get(0).getModel());
                }
            }

            if (deviceEntity != null && deviceEntity.size()> 0){
                acquisitionMiddleware.setDeviceStatus(1);
            }else {
                acquisitionMiddleware.setDeviceStatus(0);
            }
        });
        return getDataTable(list);
    }

    /**
     * 获取采集终端信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/allList")
    public AjaxResult allList() {
        List<AcquisitionMiddleware> list = acquisitionMiddlewareService.selectAcquisitionMiddlewareList(new AcquisitionMiddleware());
        return AjaxResult.success(list);
    }

    /**
     * 获取采集终端信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/unboundList")
    public AjaxResult unboundList() {
        List<AcquisitionMiddleware> list = acquisitionMiddlewareService.queryUnboundList();
        list.stream().forEach(acquisitionMiddleware -> {
//            DeviceEntity deviceEntity = deviceService.selectByMac(acquisitionMiddleware.getMac());
            List<CollectModule> collectModules = collectModuleService.selectCollectModuleByCode(acquisitionMiddleware.getCode());
            if (collectModules != null && collectModules.size()>0){
                acquisitionMiddleware.setName(collectModules.get(0).getName());
                acquisitionMiddleware.setModuleModel(collectModules.get(0).getModel());
            }else {
                List<CollectNode> collectNodes = collectNodeService.selectCollectNodeByCode(acquisitionMiddleware.getCode());
                if (collectNodes != null && collectNodes.size()>0){
                    acquisitionMiddleware.setName(collectNodes.get(0).getModuleName());
                    acquisitionMiddleware.setModuleModel(collectNodes.get(0).getModel());
                }
            }
        });
        return AjaxResult.success(list);
    }

    @Log(title = "采集终端管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('device:info:export')")
    @GetMapping("/export")
    public AjaxResult export(AcquisitionMiddleware am) {
        List<AcquisitionMiddleware> list = acquisitionMiddlewareService.selectAcquisitionMiddlewareList(am);
        ExcelUtil<AcquisitionMiddleware> util = new ExcelUtil<AcquisitionMiddleware>(AcquisitionMiddleware.class);
        return util.exportExcel(list, "采集终端数据");
    }

    /**
     * 清理无效采集终端
     */
    @PreAuthorize("@ss.hasPermi('device:info:remove')")
    @Log(title = "清理无效采集终端", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete4Invalid")
    public AjaxResult remove() {
        return AjaxResult.success(acquisitionMiddlewareService.delete4Invalid());
    }

    /**
     * 删除设备信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:remove')")
    @Log(title = "采集终端删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        List<AcquisitionMiddleware> list = Arrays.stream(ids).map(id -> acquisitionMiddlewareService.selectAcquisitionMiddlewareById(id)).collect(Collectors.toList());
        int rows = acquisitionMiddlewareService.deleteAcquisitionMiddlewareByIds(ids);
        if (rows>0)
        {
            //这边进行岸端删除同步
            list.stream().forEach(acquisitionMiddleware -> {
                AmUtil.removeAm(acquisitionMiddleware.getIp(),acquisitionMiddleware.getPort());
                acquisitionMiddlewareService.syncNewShore(acquisitionMiddleware,3);
            });
        }
        return toAjax(rows);
    }
}
