package com.xhjt.project.snapshot.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.constant.ScheduleConstants;
import com.xhjt.common.exception.job.TaskException;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.project.device.domain.TransferSnapshotVo;
import com.xhjt.project.monitor.domain.SysJob;
import com.xhjt.project.monitor.service.ISysJobService;
import com.xhjt.project.netty.service.StoreService;
import com.xhjt.project.snapshot.mapper.SnapshotTransferMapper;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 传输管理 实现类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotTransferService {
    private static final Logger logger = LoggerFactory.getLogger(SnapshotTransferService.class);

    @Autowired
    private SnapshotTransferMapper snapshotTransferMapper;
    @Autowired
    private StoreService storeService;
    @Autowired
    private ISysJobService jobService;
    @Autowired
    private ISysJobService sysJobService;
    @Autowired
    private SnapshotChannelService snapshotChannelService;
    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotTransferEntity> selectSnapshotTransferList(SnapshotTransferEntity snapshotTransfer) {
        return snapshotTransferMapper.selectSnapshotTransferList(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotTransferEntity selectSnapshotTransferById(Long id) {
        SnapshotTransferEntity snapshotTransfer = new SnapshotTransferEntity();
        snapshotTransfer.setId(id);
        return snapshotTransferMapper.selectSnapshotTransfer(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotTransferEntity selectSnapshotTransferByChannelCode(String channelCode) {
        SnapshotTransferEntity snapshotTransfer = new SnapshotTransferEntity();
        snapshotTransfer.setChannelCode(channelCode);
        return snapshotTransferMapper.selectSnapshotTransfer(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public int addSnapshotTransfer(SnapshotTransferEntity snapshotTransfer) throws TaskException, SchedulerException {

        //已存在,不添加
        if (selectSnapshotTransferByChannelCode(snapshotTransfer.getChannelCode()) != null) {
            return 0;
        }
        int rows = snapshotTransferMapper.addSnapshotTransfer(snapshotTransfer);

        if (rows == 0) {
            return 0;
        }

        //创建定时任务
        SysJob sysJob = new SysJob();
        sysJob.setJobName(snapshotTransfer.getChannelCode());
        sysJob.setJobGroup("DEFAULT");

        sysJob.setCronExpression(getCronExpression(snapshotTransfer.getCompartment()));
        sysJob.setInvokeTarget("scheduledTask.obtainScreenShot('" + snapshotTransfer.getChannelCode() + "')");
        sysJob.setConcurrent("1");
        sysJob.setMisfirePolicy("3");
        sysJob.setDisplay("1");

        rows = jobService.insertJob(sysJob);

        return rows;
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateSnapshotTransfer(SnapshotTransferEntity snapshotTransfer) throws TaskException, SchedulerException {
        SnapshotTransferEntity sourceEntity = selectSnapshotTransferById(snapshotTransfer.getId());

        if (!sourceEntity.getCompartment().equals(snapshotTransfer.getCompartment())) {
            SysJob sysJob = jobService.selectJobByName(sourceEntity.getChannelCode());
            sysJob.setCronExpression(getCronExpression(snapshotTransfer.getCompartment()));
            jobService.updateJob(sysJob);
        }
        return snapshotTransferMapper.updateSnapshotTransfer(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotTransferById(Long id) throws SchedulerException {
        //删除定时任务
        SnapshotTransferEntity snapshotTransfer = selectSnapshotTransferById(id);

        SysJob sysJob = jobService.selectJobByName(snapshotTransfer.getChannelCode());
        if (sysJob != null) {
            jobService.deleteJob(sysJob);
        }

        return snapshotTransferMapper.deleteSnapshotTransferById(id);
    }

    /**
     * 批量删除参数信息
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotTransferByIds(Long[] ids) throws SchedulerException {
        for (Long id : ids) {
            SnapshotTransferEntity snapshotTransfer = selectSnapshotTransferById(id);

            SysJob sysJob = jobService.selectJobByName(snapshotTransfer.getChannelCode());
            if (sysJob != null) {
                jobService.deleteJob(sysJob);
            }
        }
        return snapshotTransferMapper.deleteSnapshotTransferByIds(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(Long id, Integer status) throws SchedulerException {
        SnapshotTransferEntity snapshotTransfer = selectSnapshotTransferById(id);

        SysJob sysJob = sysJobService.selectJobByName(snapshotTransfer.getChannelCode());
        if (sysJob != null) {
            sysJob.setStatus(status == 1 ? ScheduleConstants.Status.NORMAL.getValue() : ScheduleConstants.Status.PAUSE.getValue());
            sysJobService.changeStatus(sysJob);
        }

        snapshotTransfer.setStatus(status);

        return snapshotTransferMapper.updateSnapshotTransfer(snapshotTransfer);
    }

    /**
     * 岸端同步过来修改状态使用
     * @param snapshotTransferEntity
     * @return
     * @throws SchedulerException
     */
    @Transactional(rollbackFor = Exception.class)
    public int changeAllStatus(SnapshotTransferEntity snapshotTransferEntity) throws SchedulerException {
        SnapshotTransferEntity snapshotTransfer = selectSnapshotTransferById(snapshotTransferEntity.getId());
        int status = 0;
        if (snapshotTransferEntity.getStatus()==1 || snapshotTransferEntity.getTransferStatus()==1){
            //存储或者传输有一个为1则为正常
            status = 1;
        }
        SysJob sysJob = sysJobService.selectJobByName(snapshotTransfer.getChannelCode());
        if (sysJob != null) {
            sysJob.setStatus(status == 1 ? ScheduleConstants.Status.NORMAL.getValue() : ScheduleConstants.Status.PAUSE.getValue());
            sysJobService.changeStatus(sysJob);
        }
        snapshotTransfer.setStatus(snapshotTransferEntity.getStatus());//存储状态
        snapshotTransfer.setTransferStatus(snapshotTransferEntity.getTransferStatus());//传输状态
        snapshotTransfer.setUpdateBy(snapshotTransferEntity.getUpdateBy());//修改人
        return snapshotTransferMapper.updateSnapshotTransfer(snapshotTransfer);
    }

    private String getCronExpression(Integer compartment) {
        if (compartment >= 60) {
            return "0 */" + compartment / 60 + " * * * ?";
        } else {
            return "*/" + compartment + " * * * * ?";
        }
    }

    /**
     * 更新到岸端
     *
     * @param transferEntity
     * @param action
     */
    public void sync2Shore(SnapshotTransferEntity transferEntity, int action) {
        SnapshotTransferEntity entity = new SnapshotTransferEntity();
        entity.setChannelCode(transferEntity.getChannelCode());
        entity.setResolvingPower(transferEntity.getResolvingPower());
        entity.setCompartment(transferEntity.getCompartment());
        entity.setCost(transferEntity.getCost());
        entity.setStatus(transferEntity.getStatus());

        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(entity), action, "snapshotTransfer");

        KafkaMessage kafkaMessage = new KafkaMessage(transferEntity.getChannelCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }


    /**
     * 更新岸端传来的数据-暂时用来同步岸端修改状态过来得
     *
     * @param syncEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleBySync(SyncEntity syncEntity) throws TaskException, SchedulerException {
        SnapshotTransferEntity syncTransfer = JSON.parseObject(syncEntity.getJsonObject(), SnapshotTransferEntity.class);
        SnapshotTransferEntity snapshotTransferEntity = selectSnapshotTransferByChannelCode(syncTransfer.getChannelCode());

        if (syncEntity.getAction() == 1 && snapshotTransferEntity == null) {
            syncTransfer.setCreateBy("sync");
            addSnapshotTransfer(syncTransfer);

        } else if (syncEntity.getAction() == 2 && snapshotTransferEntity != null) {

            snapshotTransferEntity.setResolvingPower(syncTransfer.getResolvingPower());
            snapshotTransferEntity.setCompartment(syncTransfer.getCompartment());
            snapshotTransferEntity.setCost(syncTransfer.getCost());
            snapshotTransferEntity.setStatus(syncTransfer.getStatus());
            snapshotTransferEntity.setTransferStatus(syncTransfer.getTransferStatus());
            snapshotTransferEntity.setUpdateBy("sync");

           int row = changeAllStatus(snapshotTransferEntity);
           if (row>0){
               logger.info("岸端状态修改成功---{}",snapshotTransferEntity.getChannelCode());
           }
        } else if (syncEntity.getAction() == 3 && snapshotTransferEntity != null) {
            deleteSnapshotTransferById(snapshotTransferEntity.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int changeTrStatus(Long id, Integer status) throws SchedulerException {
        SnapshotTransferEntity snapshotTransfer = selectSnapshotTransferById(id);

        SysJob sysJob = sysJobService.selectJobByName(snapshotTransfer.getChannelCode());
        if (sysJob != null) {
            sysJob.setStatus(status == 1 ? ScheduleConstants.Status.NORMAL.getValue() : ScheduleConstants.Status.PAUSE.getValue());
            sysJobService.changeStatus(sysJob);
        }
        //传输状态开启时，存储状态也开启
        if (status==1){
            snapshotTransfer.setStatus(status);//存储状态
        }
        snapshotTransfer.setTransferStatus(status);//传输状态

        return snapshotTransferMapper.updateSnapshotTransfer(snapshotTransfer);
    }

    /**
     * 修改快照通道及属性
     * @param syncEntity
     */
    public synchronized void handleAllBySync(SyncEntity syncEntity) throws TaskException, SchedulerException {
         TransferSnapshotVo transferSnapshotVo = JSON.parseObject(syncEntity.getJsonObject(), TransferSnapshotVo.class);
        if (transferSnapshotVo!=null){
            if (syncEntity.getAction() == 1){
                this.handleNewBySync(transferSnapshotVo.getSnapshotTransferEntity(), syncEntity.getAction());
                SnapshotUtil.addAllInfo();
                snapshotChannelService.handleNewBySync(transferSnapshotVo.getSnapshotChannelEntity(), syncEntity.getAction());
            }else{
                if (transferSnapshotVo.getSnapshotChannelEntity() != null){
                    //快照更新
                    snapshotChannelService.handleNewBySync(transferSnapshotVo.getSnapshotChannelEntity(), syncEntity.getAction());
                }
                if (transferSnapshotVo.getSnapshotTransferEntity() != null){
                    //传输属性更新
                    this.handleNewBySync(transferSnapshotVo.getSnapshotTransferEntity(), syncEntity.getAction());
                }
            }
        }
    }

    /**
     * 更新岸端传来的数据
     *
     * @param syncTransfer
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized void handleNewBySync(SnapshotTransferEntity syncTransfer,int action) throws TaskException, SchedulerException {
        SnapshotTransferEntity snapshotTransferEntity = selectSnapshotTransferByChannelCode(syncTransfer.getChannelCode());

        if (action == 1 && snapshotTransferEntity == null) {
            syncTransfer.setCreateBy("sync");
            addSnapshotTransfer(syncTransfer);

        } else if (action == 2 && snapshotTransferEntity != null) {
            snapshotTransferEntity.setResolvingPower(syncTransfer.getResolvingPower());
            snapshotTransferEntity.setCompartment(syncTransfer.getCompartment());
            snapshotTransferEntity.setCost(syncTransfer.getCost());
            snapshotTransferEntity.setStatus(syncTransfer.getStatus());
            snapshotTransferEntity.setUpdateBy("sync");
            int row = updateSnapshotTransfer(snapshotTransferEntity);
            if (row>0){
                logger.info("岸端同步过来通道传输属性信息修改成功----{}",snapshotTransferEntity.getChannelCode());
            }
        } else if (action == 3 && snapshotTransferEntity != null) {
            deleteSnapshotTransferById(snapshotTransferEntity.getId());
        }
    }

}
