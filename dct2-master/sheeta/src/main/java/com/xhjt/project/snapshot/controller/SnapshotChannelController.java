package com.xhjt.project.snapshot.controller;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.exception.job.TaskException;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.domain.TransferSnapshotVo;
import com.xhjt.project.monitor.domain.SysOperLog;
import com.xhjt.project.monitor.service.ISysOperLogService;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotHandlerService;
import com.xhjt.project.snapshot.service.SnapshotTransferService;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通道配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/snapshot/channel")
public class SnapshotChannelController extends BaseController {

    @Autowired
    private SnapshotChannelService snapshotChannelService;

    @Autowired
    private SnapshotHandlerService snapshotHandlerService;

    @Autowired
    private SnapshotTransferService snapshotTransferService;

    @Autowired
    private ISysOperLogService iSysOperLogService;

    /**
     * 获取截屏图片
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:edit')")
    @PostMapping("/screenshot")
    public AjaxResult screenshot(@Validated @RequestBody SnapshotChannelEntity snapshotChannel){
        String address = snapshotChannel.getAddress();
        String imagePath = snapshotHandlerService.screenshot(address);
        return AjaxResult.success(imagePath);
    }

    @PreAuthorize("@ss.hasPermi('snapshot:channel:list')")
    @RequestMapping("/list")
    public TableDataInfo list(SnapshotChannelEntity snapshotChannelEntity) {
        startPage();
        List<SnapshotChannelEntity> list = snapshotChannelService.selectSnapshotChannelList(snapshotChannelEntity);
        list.stream().forEach(snapshotChannelEntity1->{
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateS1 = df.format(new Date());
            Long operateTime = null;
            String dateS2 = "";
            if (snapshotChannelEntity1.getOperateTime()!=null){
                operateTime = snapshotChannelEntity1.getOperateTime();
                dateS2 = DateUtils.parseTimeToDate(operateTime,"yyyy-MM-dd HH:mm:ss");
            }
            try {
                if (StringUtils.isNotBlank(dateS2)){
                    Date date1 = df.parse(dateS1);
                    Date date2 = df.parse(dateS2);
                    long diff = date1.getTime() - date2.getTime();
                    long seconds = diff / 1000;
                    System.out.println(seconds+"seconds");
                    if (seconds-(snapshotChannelEntity1.getCompartment()+60)>0){
                        snapshotChannelEntity1.setStatus("0");
//                        snapshotChannelEntity1.setTrStatus(0);
//                        snapshotChannelEntity1.setTransferStatus(0);
                    }else{
                        snapshotChannelEntity1.setStatus("1");
                    }
                }

                if (snapshotChannelEntity1.getCompartment() == null || StringUtils.isBlank(dateS2)) {
                    snapshotChannelEntity1.setStatus("0");
//                    snapshotChannelEntity1.setTrStatus(0);
//                    snapshotChannelEntity1.setTransferStatus(0);
                }

            } catch (ParseException e) {
                snapshotChannelEntity1.setStatus("0");
//                snapshotChannelEntity1.setTrStatus(0);
//                snapshotChannelEntity1.setTransferStatus(0);
                logger.error("日期转义失败");
            }
        });

        return getDataTable(list);
    }

    @Log(title = "快照通道", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('snapshot:channel:export')")
    @GetMapping("/export")
    public AjaxResult export(SnapshotChannelEntity snapshotChannelEntity) {
        List<SnapshotChannelEntity> list = snapshotChannelService.selectSnapshotChannelList(snapshotChannelEntity);
        ExcelUtil<SnapshotChannelEntity> util = new ExcelUtil<SnapshotChannelEntity>(SnapshotChannelEntity.class);
        return util.exportExcel(list, "快照通道");
    }

    /**
     * 根据通道号获取配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(snapshotChannelService.selectSnapshotChannelById(id));
    }

    /**
     * 新增
     */
    @Log(title = "快照通道新增", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('snapshot:channel:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SnapshotChannelEntity snapshotChannel) throws Exception {
        snapshotChannel.setCreateBy(SecurityUtils.getUsername());
        SnapshotChannelEntity snapshotChannelEntity = snapshotChannelService.addSnapshotChannel(snapshotChannel);
        if (snapshotChannelEntity!=null) {
            //同时新增快照传输
            SnapshotTransferEntity snapshotTransfer = new SnapshotTransferEntity();
            BeanUtils.copyProperties(snapshotChannel,snapshotTransfer);
            snapshotTransfer.setStatus(1);//默认开启存储通道
            snapshotTransfer.setTransferStatus(1);//默认开启传输通道
            snapshotTransfer.setChannelCode(snapshotChannelEntity.getCode());//通道编号
            snapshotTransfer.setChannelName(snapshotChannelEntity.getName());
            int rowTransfer = snapshotTransferService.addSnapshotTransfer(snapshotTransfer);
            if (rowTransfer > 0) {
                SnapshotUtil.addAllInfo();
                logger.info("第一次添加通道，直接去获取摄像头截屏。。。。{}", System.currentTimeMillis());
                snapshotHandlerService.start(snapshotChannelEntity.getCode());
                //同步信息到岸端
                TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
                transferSnapshotVo.setSnapshotChannelEntity(snapshotChannelEntity);
                if(snapshotTransfer!=null){
                    snapshotTransfer.setConnectStatus("1");
                }
                transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
                snapshotChannelService.syncNewShore(transferSnapshotVo,1);
             }
            return toAjax(1);
        }
        return toAjax(0);
    }

    /**
     * 修改配置信息
     */
    @Log(title = "快照通道修改", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('snapshot:channel:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SnapshotChannelEntity snapshotChannel)throws TaskException, SchedulerException {
        snapshotChannel.setUpdateBy(SecurityUtils.getUsername());
        int rows = snapshotChannelService.updateSnapshotChannel(snapshotChannel);
        if (rows > 0) {
            //同时更新快照传输
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferByChannelCode(snapshotChannel.getCode());
            Long id = snapshotTransfer.getId();
            BeanUtils.copyProperties(snapshotChannel,snapshotTransfer);
            snapshotTransfer.setChannelCode(snapshotChannel.getCode());//通道编号
            snapshotTransfer.setStatus(snapshotChannel.getTrStatus());//存储状态
            snapshotTransfer.setChannelName(snapshotChannel.getName());//通道名称
            snapshotTransfer.setId(id);
            int rowTransfer = snapshotTransferService.updateSnapshotTransfer(snapshotTransfer);
            if (rowTransfer > 0) {
                SnapshotUtil.addAllInfo();
                TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
                transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
                if(snapshotTransfer!=null){
                    snapshotTransfer.setConnectStatus("1");
                }
                transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
                snapshotChannelService.syncNewShore(transferSnapshotVo, 2);
            }
        }

        return toAjax(rows);
    }

    /**
     * 删除配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:remove')")
    @Log(title = "通道删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) throws SchedulerException{
        List<SnapshotChannelEntity> list = Arrays.stream(ids).map(id -> snapshotChannelService.selectSnapshotChannelById(id)).collect(Collectors.toList());

        int rows = snapshotChannelService.deleteSnapshotChannelByIds(ids);

        if (rows > 0) {
             /*同时删除快照传输*/
            //获取对应的通道编号
            List<String> codeTransfers = list.stream().map(SnapshotChannelEntity::getCode).collect(Collectors.toList());
            //通过通道编号获取对应的快照传输数据
            List<SnapshotTransferEntity> listTransfers = codeTransfers.stream().map(code -> snapshotTransferService.selectSnapshotTransferByChannelCode(code)).collect(Collectors.toList());
            //获取对应的快照传输id进行删除
            List<Long> idTransferLists = listTransfers.stream().map(SnapshotTransferEntity::getId).collect(Collectors.toList());
            Long[] idTransfers = new Long[idTransferLists.size()];
            idTransferLists.toArray(idTransfers);
            int rowsTransfers = snapshotTransferService.deleteSnapshotTransferByIds(idTransfers);
            if (rowsTransfers > 0) {
                SnapshotUtil.addAllInfo();
                list.forEach(channel -> {
                    TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
                    transferSnapshotVo.setSnapshotChannelEntity(channel);
                    for (SnapshotTransferEntity snapshotTransferEntity:listTransfers) {
                        if (snapshotTransferEntity.getChannelCode().equalsIgnoreCase(channel.getCode())){
                            transferSnapshotVo.setSnapshotTransferEntity(snapshotTransferEntity);
                            break;
                        }
                    }
                    snapshotChannelService.syncNewShore(transferSnapshotVo, 3);
                });
            }

        }

        return toAjax(rows);
    }

    /**
     * 获取信息列表
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:list')")
    @GetMapping("/allList")
    public AjaxResult allList() {
        List<SnapshotChannelEntity> list = snapshotChannelService.selectSnapshotChannelList(new SnapshotChannelEntity());
        return AjaxResult.success(list);
    }

    /**
     * 获取信息列表
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:list')")
    @GetMapping("/unboundList")
    public AjaxResult unboundList() {
        List<SnapshotChannelEntity> list = snapshotChannelService.queryUnboundList();
        return AjaxResult.success(list);
    }

    /**
     * 定时任务-存储状态修改
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:edit')")
    @PostMapping("/changeStatus/{code}/{status}")
    public AjaxResult changeStatus(@PathVariable String code, @PathVariable Integer status) throws SchedulerException {
        SnapshotTransferEntity transferEntity = snapshotTransferService.selectSnapshotTransferByChannelCode(code);
        int row = snapshotTransferService.changeStatus(transferEntity.getId(), status);
        if (row > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(transferEntity.getId());
            SnapshotChannelEntity snapshotChannel = snapshotChannelService.selectSnapshotChannelByCode(code);
            TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
            transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
            transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
            snapshotChannelService.syncNewShore(transferSnapshotVo, 2);
        }
        if(status != null && status == 1){
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("快照通道'"+transferEntity.getChannelName()+"'(存储状态)启动");
            iSysOperLogService.insertOperlog(sysOperLog);
        }else if(status != null && status == 0){
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("快照通道'"+transferEntity.getChannelName()+"'(存储状态)关闭");
            iSysOperLogService.insertOperlog(sysOperLog);
        }
        return toAjax(row);
    }

    /**
     * 定时任务-传输状态修改
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:edit')")
    @PostMapping("/changeTrStatus/{code}/{status}")
    public AjaxResult changeTrStatus(@PathVariable String code, @PathVariable Integer status) throws SchedulerException {
        SnapshotTransferEntity transferEntity = snapshotTransferService.selectSnapshotTransferByChannelCode(code);
        int row = snapshotTransferService.changeTrStatus(transferEntity.getId(), status);
        if (row > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(transferEntity.getId());
            SnapshotChannelEntity snapshotChannel = snapshotChannelService.selectSnapshotChannelByCode(code);
            TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
            transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
            transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
            snapshotChannelService.syncNewShore(transferSnapshotVo, 2);
        }
        if(status != null && status == 1){
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("快照通道'"+transferEntity.getChannelName()+"'(传输状态)启动");
            iSysOperLogService.insertOperlog(sysOperLog);
        }else if(status != null && status == 0){
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("快照通道'"+transferEntity.getChannelName()+"'(传输状态)关闭");
            iSysOperLogService.insertOperlog(sysOperLog);
        }
        return toAjax(row);
    }
}
