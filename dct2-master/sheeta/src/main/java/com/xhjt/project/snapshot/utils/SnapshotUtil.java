package com.xhjt.project.snapshot.utils;

import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotTransferService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * class
 *
 * <AUTHOR>
 */
public class SnapshotUtil {

    protected static Logger logger = LoggerFactory.getLogger(SnapshotUtil.class);

    /**
     * 快照通道
     */
    private static Map<String, SnapshotChannelEntity> channelMap = new ConcurrentHashMap<>();

    /**
     * 快照传输
     */
    private static Map<String, SnapshotTransferEntity> transferMap = new ConcurrentHashMap<>();


    public static void addAllInfo() {
        channelMap.clear();
        transferMap.clear();

        SnapshotChannelService snapshotChannelService = SpringUtils.getBean(SnapshotChannelService.class);
        List<SnapshotChannelEntity> channelList = snapshotChannelService.selectSnapshotChannelList(null);

        channelList.forEach(channel -> channelMap.put(channel.getCode(), channel));

        SnapshotTransferService snapshotTransferService = SpringUtils.getBean(SnapshotTransferService.class);
        List<SnapshotTransferEntity> transferEntityList = snapshotTransferService.selectSnapshotTransferList(null);

        transferEntityList.forEach(transfer -> transferMap.put(transfer.getChannelCode(), transfer));
    }

    public static SnapshotChannelEntity getChannel(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        return channelMap.get(code);
    }

    public static SnapshotTransferEntity getTransfer(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        return transferMap.get(code);
    }

    public static void renewSnapshotChannel(SnapshotChannelEntity snapshotChannelEntity) {
        channelMap.put(snapshotChannelEntity.getCode(), snapshotChannelEntity);
    }

    public static void renewSnapshotTransfer(SnapshotTransferEntity snapshotTransferEntity) {
        transferMap.put(snapshotTransferEntity.getChannelCode(), snapshotTransferEntity);
    }

    public static void removeSnapshot(String code) {
        channelMap.remove(code);
        transferMap.remove(code);
    }
}
