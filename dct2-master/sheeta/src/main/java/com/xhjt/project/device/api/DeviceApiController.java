package com.xhjt.project.device.api;

import com.alibaba.fastjson.JSON;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.project.device.domain.DataStatistics;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DataStatisticsService;
import com.xhjt.project.device.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/device")
public class DeviceApiController extends BaseController {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private DataStatisticsService dataStatisticsService;

    /**
     * 修改设备传输属性
     *
     * @param jsonObject
     */
    @PostMapping("/updateBySync")
    public void updateBySync(String jsonObject) {
        DeviceEntity deviceEntity = JSON.parseObject(jsonObject, DeviceEntity.class);
        if (deviceEntity == null) {
            return;
        }
        deviceService.syncByShore(deviceEntity);
    }

    /**
     * 新增传输数据收集接口
     *
     * @param jsonObject
     */
    @PostMapping("/addDataStatics")
    public void addDataStatics(String jsonObject) {
        DataStatistics dataStatistics = JSON.parseObject(jsonObject, DataStatistics.class);
        if (dataStatistics == null) {
            return;
        }
        dataStatisticsService.addDataStatistics(dataStatistics);
    }

}
