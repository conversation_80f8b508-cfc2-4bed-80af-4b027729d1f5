package com.xhjt.project.device.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.domain.SerialConfig;
import com.xhjt.project.device.service.SerialConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 串口名称配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/serialConfig")
public class SerialConfigController extends BaseController {

    @Autowired
    private SerialConfigService serialConfigService;

    /**
     * 获取串口名称配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:serial:list')")
    @GetMapping("/list")
    public TableDataInfo list(SerialConfig serialConfig) {
        startPage();
        List<SerialConfig> list = serialConfigService.selectSerialConfigList(serialConfig);
        return getDataTable(list);
    }
    /**
     * 新增串口名称配置
     */
    @PreAuthorize("@ss.hasPermi('system:serial:add')")
    @Log(title = "串口名称配置新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SerialConfig serialConfig) {
        SerialConfig serialConfigParams = new SerialConfig();
        serialConfigParams.setOldName(serialConfig.getOldName());
        SerialConfig serialConfigOld = serialConfigService.selectSerialConfig(serialConfigParams);
        if (serialConfigOld!=null){
            //该串口已配置不能新增
            return AjaxResult.error("该串口已配置不能新增");
        }
        serialConfig.setCreateBy(SecurityUtils.getUsername());
        serialConfig.setEnable(1);//默认启用
        return toAjax(serialConfigService.addSerialConfig(serialConfig));
    }

    /**
     * 修改串口名称配置
     */
    @PreAuthorize("@ss.hasPermi('system:serial:edit')")
    @Log(title = "串口名称配置修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SerialConfig serialConfig) {
        serialConfig.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(serialConfigService.updateSerialConfig(serialConfig));
    }

    /**
     * 删除串口名称配置
     */
    @PreAuthorize("@ss.hasPermi('system:serial:remove')")
    @Log(title = "串口名称配置删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(serialConfigService.deleteSerialConfigByIds(ids));
    }

    /**
     * 根据串口ID获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:serial:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        SerialConfig serialConfig = new SerialConfig();
        serialConfig.setId(id);
        SerialConfig serialConfig1 = serialConfigService.selectSerialConfig(serialConfig);
        return AjaxResult.success(serialConfig1);
    }
}
