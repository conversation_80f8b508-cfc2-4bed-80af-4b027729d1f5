package com.xhjt.project.snapshot.controller;

import com.xhjt.common.exception.job.TaskException;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.snapshot.service.SnapshotLogService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 快照传输管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/snapshot/log")
public class SnapshotLogController extends BaseController {

    @Autowired
    private SnapshotLogService snapshotLogService;

    /**
     * 快照日志
     * @param snapshotLog
     * @return
     */
    @PreAuthorize("@ss.hasPermi('snapshot:log:list')")
    @RequestMapping("/list")
    public TableDataInfo list(SnapshotLogEntity snapshotLog) {
        startPage();
        List<SnapshotLogEntity> list = snapshotLogService.selectSnapshotLogList(snapshotLog);
        return getDataTable(list);
    }

    /**
     * 去重重写方法
     * @param keyExtractor
     * @param <T>
     * @return
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    @Log(title = "快照截图记录", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('snapshot:log:export')")
    @GetMapping("/export")
    public AjaxResult export(SnapshotLogEntity snapshotLog) {
        List<SnapshotLogEntity> list = snapshotLogService.selectSnapshotLogList(snapshotLog);
        /**Joyce*/
        return AjaxResult.success(snapshotLogService.zipwordDownAction(list));
 //      ExcelUtil<SnapshotLogEntity> util = new ExcelUtil<SnapshotLogEntity>(SnapshotLogEntity.class);
//        return util.exportExcel(list, "快照截图记录");
    }

    /**
     * 根据id获取配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(snapshotLogService.selectSnapshotLogById(id));
    }

    /**
     * 新增
     */
    @PreAuthorize("@ss.hasPermi('snapshot:log:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SnapshotLogEntity snapshotLog) throws TaskException, SchedulerException {
        snapshotLog.setCreateBy(SecurityUtils.getUsername());
        int count = snapshotLogService.addSnapshotLog(snapshotLog);
        return count > 0 ? AjaxResult.success("操作成功") : AjaxResult.error("通道已使用");
    }

    /**
     * 删除传输管理
     */
    @PreAuthorize("@ss.hasPermi('snapshot:log:remove')")
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) throws SchedulerException {
        int result = snapshotLogService.deleteSnapshotLogById(id);
        return toAjax(result);
    }

/*
    *//**
     * 根据id获取配置信息
     *//*
    @PostMapping("/screenshot/{code}")
    public AjaxResult getInfoBySnAndCode(@PathVariable String code) {
        SnapshotLogEntity snapshotLogEntity = new SnapshotLogEntity();
        snapshotLogEntity.setChannelCode(code);
        List<SnapshotLogEntity> snapshotLogEntities = snapshotLogService.selectSnapshotLogByChannel(snapshotLogEntity);
        if (snapshotLogEntities != null && snapshotLogEntities.size()>0){
            SnapshotLogEntity  returnEntity = snapshotLogEntities.get(0);
            return AjaxResult.success(returnEntity.getDirectory()+"/"+returnEntity.getFileName());
        }
        return AjaxResult.success(null);
    }*/
}
