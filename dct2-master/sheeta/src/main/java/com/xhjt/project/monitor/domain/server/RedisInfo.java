package com.xhjt.project.monitor.domain.server;

/**
 * redis参数
 * <AUTHOR>
 */
public class RedisInfo {

    /**
     * 岸上丢失数据集合
     */
    private Long shoreLoseDataSet;

    /**
     * 最大接收编号
     */
    private Integer shoreMaxReceiveNum;

    /**
     * 最大校验编号
     */
    private Integer shoreMaxCheckNum;

    /**
     * 接收与校验差
     */
    private Integer shoreCheckIntervalNum;

    public Long getShoreLoseDataSet() {
        return shoreLoseDataSet;
    }

    public void setShoreLoseDataSet(Long shoreLoseDataSet) {
        this.shoreLoseDataSet = shoreLoseDataSet;
    }

    public Integer getShoreMaxReceiveNum() {
        return shoreMaxReceiveNum;
    }

    public void setShoreMaxReceiveNum(Integer shoreMaxReceiveNum) {
        this.shoreMaxReceiveNum = shoreMaxReceiveNum;
    }

    public Integer getShoreMaxCheckNum() {
        return shoreMaxCheckNum;
    }

    public void setShoreMaxCheckNum(Integer shoreMaxCheckNum) {
        this.shoreMaxCheckNum = shoreMaxCheckNum;
    }

    public Integer getShoreCheckIntervalNum() {
        return shoreCheckIntervalNum;
    }

    public void setShoreCheckIntervalNum(Integer shoreCheckIntervalNum) {
        this.shoreCheckIntervalNum = shoreCheckIntervalNum;
    }
}
