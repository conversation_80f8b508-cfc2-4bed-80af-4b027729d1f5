package com.xhjt.project.snapshot.mapper;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;

import java.util.List;

/**
 * 传输管理
 *
 * <AUTHOR>
 */
public interface SnapshotTransferMapper {

    /**
     * 查询传输管理列表
     *
     * @param snapshotTransfer
     * @return 传输管理集合
     */
    public List<SnapshotTransferEntity> selectSnapshotTransferList(SnapshotTransferEntity snapshotTransfer);

    /**
     * 查询传输管理信息
     *
     * @param snapshotTransfer
     * @return 传输管理信息
     */
    public SnapshotTransferEntity selectSnapshotTransfer(SnapshotTransferEntity snapshotTransfer);

    /**
     * 添加
     *
     * @param snapshotTransfer
     * @return 结果
     */
    public int addSnapshotTransfer(SnapshotTransferEntity snapshotTransfer);

    /**
     * 修改传输管理
     *
     * @param snapshotTransfer
     * @return 结果
     */
    public int updateSnapshotTransfer(SnapshotTransferEntity snapshotTransfer);

    /**
     * 删除传输管理
     *
     * @param id
     * @return 结果
     */
    public int deleteSnapshotTransferById(Long id);

    /**
     * 批量删除参数信息
     *
     * @param ids
     * @return 结果
     */
    public int deleteSnapshotTransferByIds(Long[] ids);
}
