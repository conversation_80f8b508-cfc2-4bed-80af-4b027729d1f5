package com.xhjt.project.collect.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;


/**
 * 采集终端-采集节点
 *
 * <AUTHOR>
 */
public class CollectNode extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 发送的串口
     */
    private String serialPort;

    /**
     * 模块型号
     */
    private String model;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 连网配置-DHCP是否开启，0为关，1为开
     */
    private Integer netDhcp;

    /**
     * 连网配置-ip地址
     */
    private String netIp;

    /**
     * 连网配置-子网掩码
     */
    private String netSubnetMask;

    /**
     * 连网配置-网关
     */
    private String netGateway;

    /**
     * wifi配置-SSID
     */
    private String wifiSsid;

    /**
     * wifi配置-密钥
     */
    private String wifiPassword;

    /**
     * wifi配置-加密方式
     */
    private String wifiEncrypt;

    /**
     * 是否开启数据采集传输  0：否  1：是
     */
    private Integer isCollect;

    /**
     * 波特率
     */
    private Integer baudRate;

    /**
     * 数据位
     */
    private Integer dataBits;

    /**
     * 停止位
     */
    private Integer stopBits;

    /**
     * 校验位
     */
    private Integer parity;

    /**
     * 目标船舶终端服务ip
     */
    private String shipIp;

    /**
     * 采集模块名称
     */
    private String moduleName;

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSerialPort() {
        return serialPort;
    }

    public void setSerialPort(String serialPort) {
        this.serialPort = serialPort;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getNetDhcp() {
        return netDhcp;
    }

    public void setNetDhcp(Integer netDhcp) {
        this.netDhcp = netDhcp;
    }

    public String getNetIp() {
        return netIp;
    }

    public void setNetIp(String netIp) {
        this.netIp = netIp;
    }

    public String getNetSubnetMask() {
        return netSubnetMask;
    }

    public void setNetSubnetMask(String netSubnetMask) {
        this.netSubnetMask = netSubnetMask;
    }

    public String getNetGateway() {
        return netGateway;
    }

    public void setNetGateway(String netGateway) {
        this.netGateway = netGateway;
    }

    public String getWifiSsid() {
        return wifiSsid;
    }

    public void setWifiSsid(String wifiSsid) {
        this.wifiSsid = wifiSsid;
    }

    public String getWifiPassword() {
        return wifiPassword;
    }

    public void setWifiPassword(String wifiPassword) {
        this.wifiPassword = wifiPassword;
    }

    public String getWifiEncrypt() {
        return wifiEncrypt;
    }

    public void setWifiEncrypt(String wifiEncrypt) {
        this.wifiEncrypt = wifiEncrypt;
    }

    public Integer getIsCollect() {
        return isCollect;
    }

    public void setIsCollect(Integer isCollect) {
        this.isCollect = isCollect;
    }

    public Integer getBaudRate() {
        return baudRate;
    }

    public void setBaudRate(Integer baudRate) {
        this.baudRate = baudRate;
    }

    public Integer getDataBits() {
        return dataBits;
    }

    public void setDataBits(Integer dataBits) {
        this.dataBits = dataBits;
    }

    public Integer getStopBits() {
        return stopBits;
    }

    public void setStopBits(Integer stopBits) {
        this.stopBits = stopBits;
    }

    public Integer getParity() {
        return parity;
    }

    public void setParity(Integer parity) {
        this.parity = parity;
    }

    public String getShipIp() {
        return shipIp;
    }

    public void setShipIp(String shipIp) {
        this.shipIp = shipIp;
    }
}
