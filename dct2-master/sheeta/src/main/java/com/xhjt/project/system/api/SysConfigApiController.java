package com.xhjt.project.system.api;

import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.project.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/config")
public class SysConfigApiController extends BaseController {

    @Autowired
    private ISysConfigService configService;

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/laputaIp")
    public String getLaputaIp() {
        String ip = configService.selectConfigByKey("sys.laputa.ip");
        logger.info("查询IPPORT-111--{}", ip);
        return ip;
    }

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/laputaPort")
    public String getLaputaPort() {
        String port = configService.selectConfigByKey("sys.laputa.port");
        logger.info("查询IPPORT-222--{}", port);
        return port;
    }
}
