package com.xhjt.project.collect.service;

import com.xhjt.common.utils.ListUtil;
import com.xhjt.dctcore.commoncore.utils.RandomUtil;
import com.xhjt.project.collect.domain.CollectModule;
import com.xhjt.project.collect.domain.CollectNode;
import com.xhjt.project.collect.mapper.CollectNodeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * 采集节点 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CollectNodeService {
    private Logger logger = LoggerFactory.getLogger(CollectNodeService.class);

    @Autowired
    private CollectNodeMapper collectNodeMapper;
    @Autowired
    private CollectModuleService collectModuleService;

    /**
     * 查询采集节点信息
     *
     * @param collectNodeId 采集节点ID
     * @return 采集节点信息
     */
    public CollectNode selectCollectNodeById(Long collectNodeId) {
        CollectNode collectNode = new CollectNode();
        collectNode.setId(collectNodeId);
        return collectNodeMapper.selectCollectNode(collectNode);
    }

    public List<CollectNode> selectCollectNodeByCode(String code) {
        CollectNode collectNode = new CollectNode();
        collectNode.setCode(code);
        return collectNodeMapper.selectCollectNodeList(collectNode);
    }

    /**
     * 查询采集节点列表
     *
     * @param collectNode 采集节点信息
     * @return 采集节点集合
     */
    public List<CollectNode> selectCollectNodeList(CollectNode collectNode) {
        return collectNodeMapper.selectCollectNodeList(collectNode);
    }

    /**
     * 新增采集节点
     *
     * @param collectNode 采集节点信息
     * @return 结果
     */
    public int addCollectNode(CollectNode collectNode) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {

        return collectNodeMapper.addCollectNode(collectNode);
    }

    /**
     * 获取节点编码
     *
     * @return
     */
    private String getNewCode() {
        String code = RandomUtil.randomStr(5);

        List<CollectNode> list = selectCollectNodeByCode(code);
        List<CollectModule> listModules = collectModuleService.selectCollectModuleByCode(code);
        if ((list!=null && list.size() > 0) || (listModules!=null && listModules.size()>0)) {
            return getNewCode();
        }
        return code;
    }

    private String getNewSsid() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        String ssid = "XH_1";
        List<CollectNode> list = selectCollectNodeList(null);
        if (list.size() == 0) {
            return ssid;
        }

        List<String> ssidList = ListUtil.fetchFieldValueList(list, "wifiSsid");
        for (int i = 1; i < 56; i++) {
            if (!ssidList.contains("XH_" + i)) {
                ssid = "XH_" + i;
                break;
            }
        }

        return ssid;
    }

    /**
     * 修改采集节点
     *
     * @param collectNode 采集节点信息
     * @return 结果
     */
    public int updateCollectNode(CollectNode collectNode) {
        CollectNode sourceNode = selectCollectNodeById(collectNode.getId());

        sourceNode.setName(collectNode.getName());
        sourceNode.setModel(collectNode.getModel());

        sourceNode.setNetDhcp(collectNode.getNetDhcp());
        sourceNode.setNetIp(collectNode.getNetIp());
        sourceNode.setNetSubnetMask(collectNode.getNetSubnetMask());
        sourceNode.setNetGateway(collectNode.getNetGateway());

        sourceNode.setIsCollect(collectNode.getIsCollect());
        sourceNode.setBaudRate(collectNode.getBaudRate());
        sourceNode.setDataBits(collectNode.getDataBits());
        sourceNode.setStopBits(collectNode.getStopBits());
        sourceNode.setParity(collectNode.getParity());
        sourceNode.setShipIp(collectNode.getShipIp());

        return collectNodeMapper.updateCollectNode(collectNode);
    }

    /**
     * 删除采集节点信息
     *
     * @param collectNodeId 参数ID
     * @return 结果
     */
    public int deleteCollectNodeById(Long collectNodeId) {
        return collectNodeMapper.deleteCollectNodeById(collectNodeId);
    }

    /**
     * 批量删除参数信息
     *
     * @param collectNodeIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteCollectNodeByIds(Long[] collectNodeIds) {
        return collectNodeMapper.deleteCollectNodeByIds(collectNodeIds);
    }

    public void addBefore(CollectNode collectNode) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        // 生成code
        collectNode.setCode(getNewCode());
        //生成WIFI SSID
        collectNode.setWifiSsid(getNewSsid());
        collectNode.setWifiPassword("xhjt#6582850");
        collectNode.setWifiEncrypt("WPA2PSK");
    }
}
