package com.xhjt.project.snapshot.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xhjt.common.ClientListernerUtil;
import com.xhjt.common.RedisParameter;
import com.xhjt.common.utils.file.FileUtils;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.project.device.service.DataStatisticsService;
import com.xhjt.project.monitor.mapper.SysOperLogMapper;
import com.xhjt.project.snapshot.utils.ImageUtil;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import java.io.*;
import java.util.Calendar;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 截屏 服务类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotHandlerService {

    private static final Logger logger = LoggerFactory.getLogger(SnapshotHandlerService.class);

    @Value("${project.snapshotPath}")
    private String snapshotPath;

    @Value("${project.ffmpegPath}")
    private String ffmpegPath;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SnapshotLogService snapshotLogService;
    @Autowired
    private SysOperLogMapper sysOperLogMapper;
    @Autowired
    private ClientListernerUtil clientListernerUtil;
    @Autowired
    private DataStatisticsService dataStatisticsService;
    /**
     * 获取截屏图片
     */
    public void start(String channelCode) throws Exception {
        logger.info("快照开启---");
        long time = DateUtils.fetchWholeSecond(System.currentTimeMillis());
        //判断路径是否存在
        String parentDirectory = snapshotPath + "/" + channelCode;
        String directory = parentDirectory + "/" + DateUtils.getDateToString(time, DateUtils.DATE_PATTERN_NONE);
        existOrMkdirs(directory);
        String imagePath = directory + "/" + time + ".jpg";
        String zipPath = directory + "/" + time + ".zip";

        //开始截屏
        SnapshotChannelEntity snapshotChannelEntity = SnapshotUtil.getChannel(channelCode);
        SnapshotTransferEntity snapshotTransferEntity = SnapshotUtil.getTransfer(channelCode);
        if (snapshotChannelEntity == null || snapshotTransferEntity == null) {
            logger.info("快照为空-{}--",channelCode);
            return;
        }
        getPicture(snapshotChannelEntity.getAddress(), snapshotTransferEntity.getResolvingPower(), imagePath, 0,channelCode);
        File file = new File(imagePath);
        if (!file.exists()) {
            return;
        }
        //写入redis得快照采集数据量--2021-10-26
        int snapshotLength = FileUtils.pathSize(file);
        clientListernerUtil.redisSnapshotCompare(snapshotLength);
        //判断如果是传输状态启用才需要执行压缩并且发送到kafka---202110修改
        if (snapshotChannelEntity.getTransferStatus()==1){
            zipFile(zipPath, file);

            //压缩包生成完毕，发送消息到kafka提示
            KafkaMessage kafkaMessage = new KafkaMessage(1, channelCode, zipPath, snapshotTransferEntity.getCost(), time);
            send2Kafka(kafkaMessage);
        }
        // 最新图片时间
        ValueOperations<String, Long> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(RedisParameter.LATEST_PICTURE_DATE + channelCode, time);

        // 添加截屏记录
        snapshotLogService.addLogByHandler(channelCode, time, directory);

        //删除超过90天得快照记录--2021-10-25
        FileUtils.deleteStorageTime(parentDirectory);
        //删除超过90天得快照记录--2021-10-25
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH,-90);
        snapshotLogService.deleteSnapshotLogByTime(calendar.getTimeInMillis());
        dataStatisticsService.deleteDataStatisticsByTime(calendar.getTimeInMillis());
    }

    /**
     * 截屏
     */
    public void getPicture(String channelAddress, String resolvingPower, String imagePath, int tryNum,String channelCode) {
        List<String> commend = Lists.newArrayList();

        commend.add(ffmpegPath);
        commend.add("-i");
        commend.add(channelAddress);
        commend.add("-y");
        commend.add("-f");
        commend.add("image2");
//        commend.add("-t");
//        commend.add("0.001");
        commend.add("-s");
        commend.add(resolvingPower);
        commend.add(imagePath);
        Process p;
        try {
            ProcessBuilder builder = new ProcessBuilder(commend);
            builder.command(commend);
            p = builder.start();
            p.getOutputStream().close();
            doWaitFor(p);
            p.destroy();
        } catch (Exception e) {
            logger.error("截图异常。。。", e);
//            SysOperLog sysOperLog = new SysOperLog();
//            sysOperLog.setTitle("截图失败");
//            sysOperLogMapper.insertOperlog(sysOperLog);
        }
        logger.info(imagePath + ",截图成功");
        boolean b = ImageUtil.appraisalSnapshot(imagePath);
        if (!b && tryNum < 50) {
            tryNum++;
            getPicture(channelAddress, resolvingPower, imagePath, tryNum,channelCode);
        }
    }

    /**
     * 岸端-截屏
     */
    public void getAdPicture(String channelAddress, String resolvingPower, String imagePath, int tryNum,String channelCode) {
        List<String> commend = Lists.newArrayList();

        commend.add(ffmpegPath);
        commend.add("-i");
        commend.add(channelAddress);
        commend.add("-y");
        commend.add("-f");
        commend.add("image2");
//        commend.add("-t");
//        commend.add("0.001");
        commend.add("-s");
        commend.add(resolvingPower);
        commend.add(imagePath);
        Process p;
        try {
            ProcessBuilder builder = new ProcessBuilder(commend);
            builder.command(commend);
            p = builder.start();
            p.getOutputStream().close();
            doWaitFor(p);
            p.destroy();
        } catch (Exception e) {
            logger.error("截图异常。。。", e);
        }
        logger.info(imagePath + ",截图成功");

    }


    private static int doWaitFor(Process p) {
        InputStream in = null;
        InputStream err = null;
        int exitValue = -1;

        try {
            in = p.getInputStream();
            err = p.getErrorStream();
            boolean finished = false;

            while (!finished) {
                try {
                    Character c;
                    while (in.available() > 0) {
                        c = new Character((char) in.read());
                        System.out.print(c);
                    }

                    while (err.available() > 0) {
                        c = new Character((char) err.read());
                        System.out.print(c);
                    }

                    exitValue = p.exitValue();
                    finished = true;
                } catch (IllegalThreadStateException var19) {
                    Thread.currentThread();
                    Thread.sleep(500L);
                }
            }
        } catch (Exception var20) {
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException var18) {
            }

            if (err != null) {
                try {
                    err.close();
                } catch (IOException var17) {
                }
            }

        }
        return exitValue;
    }


    /**
     * 判断文件夹是否存在,不存在，则创建
     *
     * @param dirPath
     */
    private void existOrMkdirs(String dirPath) {
        File file = new File(dirPath);
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    /**
     * zip 压缩
     *
     * @param zipFileName
     * @param inputFile
     * @throws Exception
     */
    private void zip(String zipFileName, File inputFile) throws Exception {
        ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFileName));
        zip(out, inputFile, "");
        out.close();
    }

    private void zipFile(String zipFileName, File inputFile) throws Exception {
        ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFileName));
        zip(out, inputFile, inputFile.getName());
        out.close();
    }

    /**
     * 方法重载
     *
     * @param out
     * @param f
     * @param base
     * @throws Exception
     */
    private void zip(ZipOutputStream out, File f, String base) throws Exception {
        if (f.isDirectory()) {
            File[] fl = f.listFiles();
            for (int i = 0; i < fl.length; i++) {
                zip(out, fl[i], base + fl[i].getName());
            }
        } else {
            // 创建新的进入点
            out.putNextEntry(new ZipEntry(base));
            // 创建FileInputStream对象
            FileInputStream in = new FileInputStream(f);
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            in.close();
        }
    }

    /**
     * 点击预览截屏
     * ffmpegPath
     *      -i address:address表示输入文件（rtsp地址）
     *      -y :默认自动覆盖输出文件，而不再询问确认
     *      -f image2:image2表示输入输出格式
     *      -s 320x240 imagePath:320x240表示分表率  imagePath表示存储文件地址
     */
    public String screenshot(String address) {
        long time = DateUtils.fetchWholeSecond(System.currentTimeMillis());
        //判断路径是否存在
        String directory = snapshotPath + "/temporary";
        FileUtils.delAllFile(directory);
        existOrMkdirs(directory);
        String imagePath = directory + "/" + time + ".jpg";

        //开始截屏
        logger.info("获取摄像头截屏。。。。{}", System.currentTimeMillis());
        List<String> commend = Lists.newArrayList();
        commend.add(ffmpegPath);
        commend.add("-i");
        commend.add(address);
        commend.add("-y");
        commend.add("-f");
        commend.add("image2");
        commend.add("-s");
        commend.add("320x240");
        commend.add(imagePath);
        Process p;
        try {
            ProcessBuilder builder = new ProcessBuilder(commend);
            builder.command(commend);
            p = builder.start();
            p.getOutputStream().close();
            doWaitFor(p);
            p.destroy();
        } catch (Exception e) {
            logger.error("截图异常。。。", e);
        }
        logger.info(imagePath + ",截图成功");

        return imagePath;
    }

    /**
     * 发送数据到kafka
     *
     * @param message
     */
    private void send2Kafka(KafkaMessage message) {
        //发送消息，topic不存在将自动创建新的topic
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send("ship_snapshot_data_topic", JSONObject.toJSONString(message));
        //添加成功发送消息的回调和失败的回调
        listenableFuture.addCallback(
                result -> {
                },
                ex -> logger.info("send message to {} failure,error message:{}", "ship_image_data_topic", ex.getMessage()));
    }

    /**
     * 获取截屏图片-岸端预览
     */
    public void startNew(SyncEntity syncEntity) throws Exception {
        SnapshotChannelEntity snapshotChannelEntity = JSON.parseObject(syncEntity.getJsonObject(), SnapshotChannelEntity.class);
        String channelCode = snapshotChannelEntity.getCode();
        long time = DateUtils.fetchWholeSecond(System.currentTimeMillis());
        //判断路径是否存在
        String parentDirectory = snapshotPath + "/" + channelCode;
        String directory = parentDirectory + "/" + DateUtils.getDateToString(time, DateUtils.DATE_PATTERN_NONE);
        existOrMkdirs(directory);
        String imagePath = directory + "/" + time + ".jpg";
        String zipPath = directory + "/" + time + ".zip";
        //开始截屏
        if (snapshotChannelEntity == null) {
            return;
        }
        getAdPicture(snapshotChannelEntity.getAddress(), snapshotChannelEntity.getResolvingPower(), imagePath, 0,channelCode);
        File file = new File(imagePath);
        zipFile(zipPath, file);

        //压缩包生成完毕，发送消息到kafka提示
        KafkaMessage kafkaMessage = new KafkaMessage(1, channelCode, zipPath, 10, time);
        logger.info("截屏完毕，开始传输到kafka进行处理------{}-----------",imagePath,kafkaMessage.toString());
        send2Kafka(kafkaMessage);
    }
}
