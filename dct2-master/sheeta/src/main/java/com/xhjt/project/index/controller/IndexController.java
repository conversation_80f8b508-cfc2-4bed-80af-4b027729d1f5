package com.xhjt.project.index.controller;


import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.index.domain.ShipTerminalManage;
import com.xhjt.project.index.service.*;
import com.xhjt.project.netty.handler.ChannelManagerHandler;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/index")
public class IndexController {
    @Autowired
    private IndexService indexService;

    /**
     * 首页-硬盘使用率
     */
    @RequestMapping("/getDeskUsage")
    public AjaxResult getDeskUsage() throws IOException {
        return AjaxResult.success(indexService.getDeskUsage());
    }

    /**
     * 首页-时钟信息
     */
    @RequestMapping("/date")
    public AjaxResult getSysDate(){
        return AjaxResult.success(indexService.getSysDate());
    }

    /**
     * 首页-运行时长
     */
    @RequestMapping("/runTime")
    public AjaxResult getRunTime(){
        return AjaxResult.success(indexService.getRunTime());
    }

    /**
     * 首页-设备接入数
     */
    @RequestMapping("/accessCount")
    public AjaxResult accessCount(){
        Integer count = 0;
        Integer allCount = 0;
        if(indexService.seletctDeviceAllCount() != null){
            allCount = indexService.seletctDeviceAllCount();
        }
        if(indexService.seletctDeviceCount() != null){
            count = indexService.seletctDeviceCount();
        }
        List<Integer> result = new ArrayList<Integer>();
        result.add(count);
        result.add(allCount);
        return AjaxResult.success(result);
    }

    /**
     * 首页-设备参数量
     */
    @RequestMapping("/deviceParamsCount")
    public AjaxResult deviceParamsCount(){
        Integer count = 0;
        Integer allCount = 0;
        List<Integer> types = indexService.seletctDeviceAllCountList();
        for (Integer type:types) {
            if(indexService.selectDeviceAttributeCount(type) != null){
                count = allCount + indexService.selectDeviceAttributeCount(type);
            }
        }
        if(indexService.selectTransferAttributeCount() != null){
            allCount = indexService.selectTransferAttributeCount();
        }
        List<Integer> result = new ArrayList<Integer>();
        result.add(count);
        result.add(allCount);
        return AjaxResult.success(result);
    }

    /**
     * 首页-快照通道数
     */
    @RequestMapping("/snapshotChannelCount")
    public AjaxResult snapshotChannelCount(){
        List<Integer> result = indexService.snapshotChannelCount();
        return AjaxResult.success(result);
    }

    /**
     *  首页-设备采集传输情况
     */
    @GetMapping("/acquisitionTransmission")
    public AjaxResult acquisitionTransmission(String type){
        return AjaxResult.success(indexService.getData(type));
    }

    /**
     *  首页-平均传输速率-设备
     */
    @GetMapping("/dataDeviceList")
    public AjaxResult dataDeviceList(String type){
        return AjaxResult.success(indexService.getDeviceDataList(type));
    }

    /**
     *  首页-平均传输速率-快照
     */
    @GetMapping("/dataSnapshotList")
    public AjaxResult dataSnapshotList(String type){
        return AjaxResult.success(indexService.getSnapshotDataList(type));
    }

    /**
     *  首页-系统运行状态
     */
    @GetMapping("/sysRunStatus")
    public AjaxResult deviceRunStatus(){
        return AjaxResult.success(indexService.sysRunStatus());
    }

    /**
     * 获取船舶终端管理信息-岸端使用
     */
    @GetMapping("/getShipTerminalManage")
    public AjaxResult getShipTerminalManage(){
        ShipTerminalManage shipTerminalManage = null;
        try {
            shipTerminalManage = indexService.getShipTerminalManage();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return AjaxResult.success(shipTerminalManage);
    }
}
