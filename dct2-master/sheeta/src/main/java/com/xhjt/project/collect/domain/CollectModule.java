package com.xhjt.project.collect.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;


/**
 * 采集终端-采集模块
 *
 * <AUTHOR>
 */
public class CollectModule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 发送的串口
     */
    private String serialPort;

    /**
     * 模块型号
     */
    private String model;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 连网配置-连接的采集节点编码
     */
    private String nodeCode;

    /**
     * 连网配置-连接的采集节点序号
     */
    private Integer nodeNum;

    /**
     * 波特率
     */
    private Integer baudRate;

    /**
     * 数据位
     */
    private Integer dataBits;

    /**
     * 停止位
     */
    private Integer stopBits;

    /**
     * 校验位
     */
    private Integer parity;

    /**
     * 目标船舶终端服务ip
     */
    private String shipIp;

    private String nodeName;

    public String getNodeName() {
        return nodeName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSerialPort() {
        return serialPort;
    }

    public void setSerialPort(String serialPort) {
        this.serialPort = serialPort;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public Integer getNodeNum() {
        return nodeNum;
    }

    public void setNodeNum(Integer nodeNum) {
        this.nodeNum = nodeNum;
    }

    public Integer getBaudRate() {
        return baudRate;
    }

    public void setBaudRate(Integer baudRate) {
        this.baudRate = baudRate;
    }

    public Integer getDataBits() {
        return dataBits;
    }

    public void setDataBits(Integer dataBits) {
        this.dataBits = dataBits;
    }

    public Integer getStopBits() {
        return stopBits;
    }

    public void setStopBits(Integer stopBits) {
        this.stopBits = stopBits;
    }

    public Integer getParity() {
        return parity;
    }

    public void setParity(Integer parity) {
        this.parity = parity;
    }

    public String getShipIp() {
        return shipIp;
    }

    public void setShipIp(String shipIp) {
        this.shipIp = shipIp;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }
}
