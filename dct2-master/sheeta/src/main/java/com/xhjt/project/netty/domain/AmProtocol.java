package com.xhjt.project.netty.domain;

import com.xhjt.project.device.domain.AcquisitionMiddleware;
import io.netty.buffer.ByteBuf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * <br>
 * <b>功能描述: 传输协议</b>
 * <p/>
 *
 * <AUTHOR>
 * @since 1.0
 */
public class AmProtocol {
    protected Logger logger = LoggerFactory.getLogger(AmProtocol.class);

    /**
     * 设备类型
     */
    private int type;
    /**
     * 设备名称
     */
    private String name;

    private String mac;

    /**
     * 消息的内容
     */
    private byte[] content;

    private Long time;

    public AmProtocol(ByteBuf byteBuf, AcquisitionMiddleware am) {
        byte[] allBytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(allBytes);

        String allStr = new String(allBytes, StandardCharsets.UTF_8);
        logger.info("原始数据：{}",allStr);
        if (allStr.contains("&&&")) {
            if (allStr.length() <= 48) {
                return;
            }
            int headLen = allStr.indexOf("@@");
            String headStr = allStr.substring(0, headLen + 2);
            headStr = headStr.replace("&&&", "");
            headStr = headStr.replace("@@", "");
            String[] headArr = headStr.split("#");
//            if (am!=null && am.getType()!=null){
//                this.type = am.getType();
//            }else{
//                this.type = 0;
//            }
            this.name = headArr[1];
            this.mac = headArr[2];

            byteBuf.readerIndex(headLen + 2);
            this.content = new byte[byteBuf.readableBytes()];
            byteBuf.readBytes(this.content);
        } else {
//            this.type = am.getType();
            this.name = am.getName();
            this.mac = am.getMac();

            this.content = allBytes;
        }

        this.time = System.currentTimeMillis();
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }
}