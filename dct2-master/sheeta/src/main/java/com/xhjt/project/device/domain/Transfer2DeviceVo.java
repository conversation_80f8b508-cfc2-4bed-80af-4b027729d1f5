package com.xhjt.project.device.domain;

import java.util.List;

/**
 * Created by chen<PERSON><PERSON>g on 2021/10/23.
 */
public class Transfer2DeviceVo {
    private DeviceStatus deviceStatus;
    private List<Transfer2SheetaEntity> transferAttributeEntities;

    public DeviceStatus getDeviceStatus() {
        return deviceStatus;
    }

    public void setDeviceStatus(DeviceStatus deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public List<Transfer2SheetaEntity> getTransferAttributeEntities() {
        return transferAttributeEntities;
    }

    public void setTransferAttributeEntities(List<Transfer2SheetaEntity> transferAttributeEntities) {
        this.transferAttributeEntities = transferAttributeEntities;
    }
}
