package com.xhjt.project.device.service;

import com.xhjt.project.device.domain.DataStatistics;
import com.xhjt.project.device.mapper.DataStatisticsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 采集-传输数据收集
 *
 * <AUTHOR>
 */
@Service
public class DataStatisticsService {

    @Autowired
    private DataStatisticsMapper dataStatisticsMapper;


    /**
     * 查询
     *
     * @param dataStatistics
     * @return  集合
     */
    @Transactional(rollbackFor = Exception.class)
    public List<DataStatistics> selectDataStatisticsList(DataStatistics dataStatistics) {
        return dataStatisticsMapper.selectDataStatisticsList(dataStatistics);
    }

    /**
     * 新增
     *
     * @param dataStatistics
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addDataStatistics(DataStatistics dataStatistics) {
        return dataStatisticsMapper.addDataStatistics(dataStatistics);
    }
    /**
     * 通过时间删除
     * @param timeInMillis
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataStatisticsByTime(long timeInMillis) {
        //传过来的是距离现在90天的时间戳
        dataStatisticsMapper.deleteDataStatisticsByTime(timeInMillis);
    }

}
