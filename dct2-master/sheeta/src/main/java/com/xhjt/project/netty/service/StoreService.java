package com.xhjt.project.netty.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.DateUtils;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * TCP消息存储处理服务类
 *
 * <AUTHOR>
 */
@Service
public class StoreService {
    public final static Logger logger = LoggerFactory.getLogger(StoreService.class);

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    private static String SOURCE_DATA_TOPIC = "ship_device_source_data_topic";

    private static String SYNC_DATA_TOPIC = "ship_sync_data_topic";

    private static String RAW_DATA_TOPIC = "ship_raw_data_topic";

    @Value("${project.sourceDataPath}")
    private String sourceDataPath;

    /**
     * 发送数据到kafka
     *
     * @param message
     */
    public void send2Kafka(KafkaMessage message) {
        //发送消息，topic不存在将自动创建新的topic
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send(SOURCE_DATA_TOPIC, JSONObject.toJSONString(message));
        //添加成功发送消息的回调和失败的回调
        listenableFuture.addCallback(
                result -> {
                },
                ex -> logger.info("send message to {} failure,error message:{}", SOURCE_DATA_TOPIC, ex.getMessage()));
    }

    /**
     * 数据存入txt文件
     *
     * @param message
     */
    public void save2Txt(KafkaMessage message) {
        PrintWriter fw;
        String directory = sourceDataPath + "/" + DeviceTypeEnum.getByValue(message.getType()).getAlias() + "-" + message.getCode();
        File file = new File(directory);
        if (!file.exists()) {
            file.mkdirs();
        }

        String fileFullPath = directory + "/" + DateUtils.getDate() + ".txt";

        try {
            fw = new PrintWriter(new BufferedWriter(new FileWriter(fileFullPath, true)));

            // 加入获取时间
            String timeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(message.getInitialTime()));
            fw.println("@&" + timeStr + "&@");

            fw.println(message.getMsg());
            fw.flush();
            fw.close();
        } catch (Exception e) {
            System.out.println();
            logger.error("写入文件时报错！", e);
        }
    }

    /**
     * 发送数据到kafka
     *
     * @param message
     */
    public void sendSync2Kafka(KafkaMessage message) {
        //发送消息，topic不存在将自动创建新的topic
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send(SYNC_DATA_TOPIC, JSONObject.toJSONString(message));
        //添加成功发送消息的回调和失败的回调
        listenableFuture.addCallback(
                result -> {
                },
                ex -> logger.info("send message to {} failure,error message:{}", SYNC_DATA_TOPIC, ex.getMessage()));
    }

    /**
     * 发送原始数据到kafka
     *
     * @param message
     */
    public void sendRawData2Kafka(KafkaMessage message) {
        //发送消息，topic不存在将自动创建新的topic
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send(RAW_DATA_TOPIC, JSONObject.toJSONString(message));
        //添加成功发送消息的回调和失败的回调
        listenableFuture.addCallback(
                result -> {
                },
                ex -> logger.info("send message to {} failure,error message:{}", RAW_DATA_TOPIC, ex.getMessage()));
    }
}
