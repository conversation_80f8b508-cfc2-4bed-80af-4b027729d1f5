package com.xhjt.project.collect.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.collect.domain.CollectNode;
import com.xhjt.project.collect.service.CollectNodeService;
import com.xhjt.project.collect.service.WriteCmdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采集节点 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/collect/node")
public class CollectNodeController extends BaseController {

    @Autowired
    private CollectNodeService collectNodeService;
    @Autowired
    private WriteCmdService writeCmdService;

    /**
     * 获取采集节点列表
     */
    @PreAuthorize("@ss.hasPermi('collect:node:list')")
    @GetMapping("/list")
    public TableDataInfo list(CollectNode collectNode) {
        startPage();
        List<CollectNode> list = collectNodeService.selectCollectNodeList(collectNode);
        return getDataTable(list);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('collect:node:query')")
    @GetMapping(value = "/{collectNodeId}")
    public AjaxResult getInfo(@PathVariable Long collectNodeId) {
        return AjaxResult.success(collectNodeService.selectCollectNodeById(collectNodeId));
    }

    /**
     * 新增采集节点
     */
    @PreAuthorize("@ss.hasPermi('collect:node:add')")
    @Log(title = "采集节点新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CollectNode collectNode) throws Exception {
        collectNode.setCreateBy(SecurityUtils.getUsername());
        collectNodeService.addBefore(collectNode);
        //写入配置
        writeCmdService.writeConfig(collectNode);
        int row = collectNodeService.addCollectNode(collectNode);
        return toAjax(row);
    }

    /**
     * 修改采集节点
     */
    @PreAuthorize("@ss.hasPermi('collect:node:edit')")
    @Log(title = "采集节点修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody CollectNode collectNode) throws Exception {
        collectNode.setUpdateBy(SecurityUtils.getUsername());
        int row = collectNodeService.updateCollectNode(collectNode);

        //写入配置
        writeCmdService.writeConfig(collectNode);
        return toAjax(row);
    }

    /**
     * 删除采集节点
     */
    @PreAuthorize("@ss.hasPermi('collect:node:remove')")
    @Log(title = "采集节点删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{collectNodeIds}")
    public AjaxResult remove(@PathVariable Long[] collectNodeIds) {
        return toAjax(collectNodeService.deleteCollectNodeByIds(collectNodeIds));
    }

    /**
     * 获取采集节点下拉列表
     */
    @PreAuthorize("@ss.hasPermi('collect:node:list')")
    @GetMapping("/listNodes")
    public AjaxResult listNodes() {
        List<CollectNode> list = collectNodeService.selectCollectNodeList(null);
        return AjaxResult.success(list);
    }
}
