package com.xhjt.project.device.domain;

/**
 * 采集终端配置回写实体
 *
 * <AUTHOR>
 */
public class WriteSerialPortEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 串口号
     */
    private String serialPort;

    /**
     * 模块型号
     */
    private String moduleModel;

    /**
     * 采集节点名称
     */
    private String name;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 子网掩码
     */
    private String mask;

    /**
     * 网关
     */
    private String gateway;

    /**
     * 采集节点数据传输状态
     */
    private Integer states;

    /**
     * 波特率
     */
    private Integer baudRate;

    /**
     * 数据位
     */
    private Integer dataBits;

    /**
     * 停止位
     */
    private Integer stopBits;

    /**
     * 校验位
     */
    private Integer parity;

    /**
     * 船舶终端服务IP
     */
    private String shipIp;

    public String getSerialPort() {
        return serialPort;
    }

    public void setSerialPort(String serialPort) {
        this.serialPort = serialPort;
    }

    public String getModuleModel() {
        return moduleModel;
    }

    public void setModuleModel(String moduleModel) {
        this.moduleModel = moduleModel;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMask() {
        return mask;
    }

    public void setMask(String mask) {
        this.mask = mask;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public Integer getStates() {
        return states;
    }

    public void setStates(Integer states) {
        this.states = states;
    }

    public Integer getBaudRate() {
        return baudRate;
    }

    public void setBaudRate(Integer baudRate) {
        this.baudRate = baudRate;
    }

    public Integer getDataBits() {
        return dataBits;
    }

    public void setDataBits(Integer dataBits) {
        this.dataBits = dataBits;
    }

    public Integer getStopBits() {
        return stopBits;
    }

    public void setStopBits(Integer stopBits) {
        this.stopBits = stopBits;
    }

    public Integer getParity() {
        return parity;
    }

    public void setParity(Integer parity) {
        this.parity = parity;
    }

    public String getShipIp() {
        return shipIp;
    }

    public void setShipIp(String shipIp) {
        this.shipIp = shipIp;
    }
}
