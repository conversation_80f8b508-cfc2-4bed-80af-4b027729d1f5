package com.xhjt.project.device.controller;

import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.domain.ConnectLog;
import com.xhjt.project.device.service.ConnectLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 设备信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/connectLog")
public class ConnectLogController extends BaseController {

    @Autowired
    private ConnectLogService connectLogService;

    /**
     * 获取采集终端日志列表
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConnectLog connectLog) {
        startPage();
        List<ConnectLog> list = connectLogService.selectConnectLogList(connectLog);
        return getDataTable(list);
    }

    @Log(title = "采集终端日志管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('device:info:export')")
    @GetMapping("/export")
    public AjaxResult export(ConnectLog connectLog) {
        List<ConnectLog> list = connectLogService.selectConnectLogList(connectLog);
        ExcelUtil<ConnectLog> util = new ExcelUtil<ConnectLog>(ConnectLog.class);
        return util.exportExcel(list, "采集终端日志数据");
    }

}
