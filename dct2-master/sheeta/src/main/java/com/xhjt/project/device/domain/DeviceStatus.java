package com.xhjt.project.device.domain;

/**
 * 设备
 *
 * <AUTHOR>
 */
public class DeviceStatus{

    /**
     * 设备编码
     */
    private String code;
    /**
     * 存储状态
     */
    private Integer enable;

    /**
     * TCP-串口连接状态
     */
    private Integer connectStatus;

    /**
     * 快照传输状态
     */
    private Integer transferStatus;

    /**
     * 传输间隔(单位：秒)
     */
    private Integer compartment;

    public Integer getCompartment() {
        return compartment;
    }

    public void setCompartment(Integer compartment) {
        this.compartment = compartment;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public Integer getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(Integer connectStatus) {
        this.connectStatus = connectStatus;
    }

    public Integer getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(Integer transferStatus) {
        this.transferStatus = transferStatus;
    }
}
