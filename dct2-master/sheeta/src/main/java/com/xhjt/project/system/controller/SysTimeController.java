package com.xhjt.project.system.controller;


import com.xhjt.common.constant.UserConstants;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.system.domain.SysDept;
import com.xhjt.project.system.service.ISysTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 修改系统时间
 */
@RestController
@RequestMapping("/system/time")
public class SysTimeController {
    @Autowired
    private ISysTimeService iSysTimeService;

    @PostMapping("/manual")
    public AjaxResult editTimeByManual(String time) {
        iSysTimeService.editTime(time);
        return AjaxResult.success();
    }

    @PostMapping("/synchronization")
    public AjaxResult synchronizationTime() {
        iSysTimeService.synchronizationTime();
        return AjaxResult.success();
    }

    @GetMapping("/getTime")
    public AjaxResult getTime(){
        return AjaxResult.success("200",iSysTimeService.getTime());
    }
}
