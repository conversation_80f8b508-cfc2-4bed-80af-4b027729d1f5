package com.xhjt.project.index.mapper;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;

import java.util.List;

public interface AdviceEquipmentAccessMapper {
    Integer seletctDeviceAllCount();

    Integer seletctDeviceCount();

    Integer selectDeviceAttributeCount(Integer type);

    Integer selectTransferAttributeCount();

    Integer seletctSnapshotAllCount();

    List<SnapshotChannelEntity> selectSnapshotChannelAllList();

    List<Integer> seletctDeviceAllCountList();

}
