package com.xhjt.project.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * class
 *
 * <AUTHOR>
 */
@FeignClient(name = "duffy", url = "${feign.duffyUrl}")
public interface DuffyApi {


    /**
     * 设备创建hbase表
     *
     * @param deviceType
     * @param deviceCode
     * @return
     */
    @PostMapping("/device/createTable")
    void createTable(@RequestParam(value = "deviceType") Integer deviceType, @RequestParam(value = "deviceCode") String deviceCode);

    /**
     * 设备删除hbase表
     *
     * @param deviceType
     * @param deviceCode
     * @return
     */
    @PostMapping("/device/removeTable")
    void removeTable(@RequestParam(value = "deviceType") Integer deviceType, @RequestParam(value = "deviceCode") String deviceCode);

}
