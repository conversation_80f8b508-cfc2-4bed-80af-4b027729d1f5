package com.xhjt.project.system.service.impl;

import com.xhjt.project.system.service.ISysTimeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Service
public class ISysTimeServiceImpl implements ISysTimeService {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Override
    public void editTime(String time) {
        String cmd = ("date -s \"" + time) + "\"";
        String[] cmdA = { "/bin/sh", "-c", cmd };
        Process p = null;
        try {
            p = Runtime.getRuntime().exec(cmdA);
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            reader.close();
            p.waitFor();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void synchronizationTime() {
        String cmd = ("systemctl restart chronyd");
        Process p = null;
        try {
            p = Runtime.getRuntime().exec(cmd);
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            reader.close();
            p.waitFor();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public Object getTime() {
        SimpleDateFormat df= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(new Date());
    }
}
