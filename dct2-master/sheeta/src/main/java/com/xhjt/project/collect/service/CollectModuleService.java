package com.xhjt.project.collect.service;

import com.xhjt.common.utils.ListUtil;
import com.xhjt.dctcore.commoncore.utils.RandomUtil;
import com.xhjt.project.collect.domain.CollectModule;
import com.xhjt.project.collect.mapper.CollectModuleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * 采集模块 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CollectModuleService {
    @Autowired
    private CollectModuleMapper collectModuleMapper;


    /**
     * 查询采集模块信息
     *
     * @param collectModuleId 采集模块ID
     * @return 采集模块信息
     */
    public CollectModule selectCollectModuleById(Long collectModuleId) {
        CollectModule collectModule = new CollectModule();
        collectModule.setId(collectModuleId);
        return collectModuleMapper.selectCollectModule(collectModule);
    }

    /**
     * 查询采集模块列表
     *
     * @param collectModule 采集模块信息
     * @return 采集模块集合
     */
    public List<CollectModule> selectCollectModuleList(CollectModule collectModule) {
        return collectModuleMapper.selectCollectModuleList(collectModule);
    }

    public List<CollectModule> selectCollectModuleByCode(String code) {
        CollectModule collectModule = new CollectModule();
        collectModule.setCode(code);
        return collectModuleMapper.selectCollectModuleList(collectModule);
    }

    /**
     * 新增采集模块
     *
     * @param collectModule 采集模块信息
     * @return 结果
     */
    public int addCollectModule(CollectModule collectModule) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {

        return collectModuleMapper.addCollectModule(collectModule);
    }

    /**
     * 获取模块编码
     *
     * @return
     */
    private String getNewCode() {
        String code = RandomUtil.randomStr(5);

        List<CollectModule> list = selectCollectModuleByCode(code);
        if (list.size() > 0) {
            return getNewCode();
        }
        return code;
    }

    /**
     * 获取节点连接序号
     *
     * @return
     */
    private Integer getNodeNum(String nodeCode) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        int num = 1;
        CollectModule queryVo = new CollectModule();
        queryVo.setNodeCode(nodeCode);
        List<CollectModule> list = selectCollectModuleList(queryVo);
        if (list == null || list.size() == 0) {
            return num;
        }

        List<Integer> numList = ListUtil.fetchIntegerFieldValueList(list, "nodeNum");
        for (int i = 1; i < 100; i++) {
            if (!numList.contains(i)) {
                num = i;
                break;
            }
        }
        return num;
    }

    /**
     * 修改采集模块
     *
     * @param collectModule 采集模块信息
     * @return 结果
     */
    public int updateCollectModule(CollectModule collectModule) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        CollectModule sourceModule = selectCollectModuleById(collectModule.getId());

        sourceModule.setName(collectModule.getName());
        sourceModule.setModel(collectModule.getModel());

        if (!collectModule.getNodeCode().equals(sourceModule.getNodeCode())) {
            sourceModule.setNodeCode(collectModule.getNodeCode());
            sourceModule.setNodeNum(getNodeNum(collectModule.getNodeCode()));
        }

        sourceModule.setBaudRate(collectModule.getBaudRate());
        sourceModule.setDataBits(collectModule.getDataBits());
        sourceModule.setParity(collectModule.getParity());
        sourceModule.setStopBits(collectModule.getStopBits());
        sourceModule.setShipIp(collectModule.getShipIp());
        return collectModuleMapper.updateCollectModule(collectModule);
    }

    /**
     * 删除采集模块信息
     *
     * @param collectModuleId 参数ID
     * @return 结果
     */
    public int deleteCollectModuleById(Long collectModuleId) {
        return collectModuleMapper.deleteCollectModuleById(collectModuleId);
    }

    /**
     * 批量删除参数信息
     *
     * @param collectModuleIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteCollectModuleByIds(Long[] collectModuleIds) {
        return collectModuleMapper.deleteCollectModuleByIds(collectModuleIds);
    }


    public void addBefore(CollectModule collectModule) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        // 生成code
        collectModule.setCode(getNewCode());
        // 连接的节点的序号
        collectModule.setNodeNum(getNodeNum(collectModule.getNodeCode()));
    }
}
