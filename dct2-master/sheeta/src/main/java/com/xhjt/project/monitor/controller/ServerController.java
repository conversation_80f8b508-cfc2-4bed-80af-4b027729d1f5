package com.xhjt.project.monitor.controller;

import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.monitor.domain.Server;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务器监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/server")
public class ServerController {
    @GetMapping()
    public AjaxResult getInfo() throws Exception {
        Server server = new Server();
        server.copyTo();
        return AjaxResult.success(server);
    }
}
