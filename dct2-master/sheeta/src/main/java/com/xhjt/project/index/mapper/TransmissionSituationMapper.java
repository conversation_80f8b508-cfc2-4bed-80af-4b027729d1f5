package com.xhjt.project.index.mapper;

import com.xhjt.project.index.domain.DataList;

import java.util.List;

public interface TransmissionSituationMapper {
    Double getAcquisition(String type);

    Double getAcacquisitionDevice(String type);

    Double getAcquisitionSnapshot(String type);

    Double getTransmission(String type);

    Double getTransmissionDevice(String type);

    Double getTransmissionSnapshot(String type);

    List<DataList> getDeviceDataList(String type);

    List<DataList> getSnapshotDataList(String type);
}
