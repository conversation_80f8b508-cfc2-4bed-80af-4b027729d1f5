package com.xhjt.project.netty.common;

import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * class
 *
 * <AUTHOR>
 */
public class DeviceUtil {

    protected static Logger logger = LoggerFactory.getLogger(DeviceUtil.class);

    /**
     * 设备
     */
    private static Map<String, DeviceEntity> deviceMap = new ConcurrentHashMap<>();

    /**
     * 设备优先级
     */
    private static Map<String, Integer> deviceCostMap = new ConcurrentHashMap<>();

    /**
     * 设备编号
     */
    private static Map<String, String> deviceCodeMap = new ConcurrentHashMap<>();


    public static void addAllDevice() {
        deviceMap.clear();
        deviceCostMap.clear();
        deviceCodeMap.clear();

        DeviceService deviceService = SpringUtils.getBean(DeviceService.class);
        List<DeviceEntity> list = deviceService.selectDeviceList(null);

        for (DeviceEntity device : list) {
            String mac = "";
            if (device.getConnectType() == 1){
                mac = device.getSerialPort()+device.getCode();
            }else {
                mac = device.getMac()+device.getCode();
            }
            deviceMap.put(mac, device);
            deviceCostMap.put(mac, device.getCost());
            deviceCodeMap.put(mac, device.getCode());
        }
    }

    public static DeviceEntity getDevice(String mac) {
        if (StringUtils.isBlank(mac)) {
            return null;
        }

        return deviceMap.get(mac);
    }

    public static Integer getDeviceCost(String mac) {
        if (StringUtils.isBlank(mac)) {
            return null;
        }

        return deviceCostMap.get(mac);
    }

    public static String getDeviceCode(String mac) {
        if (StringUtils.isBlank(mac)) {
            return null;
        }

        return deviceCodeMap.get(mac);
    }

    public static void renewDevice(DeviceEntity device) {
        String mac = "";
        if (device.getConnectType() == 1){
            mac = device.getSerialPort()+device.getCode();
        }else {
            mac = device.getMac()+device.getCode();
        }
        deviceMap.put(mac, device);
        deviceCostMap.put(mac, device.getCost());
        deviceCodeMap.put(mac, device.getCode());
    }

    public static void removeDevice(String mac) {
        deviceMap.remove(mac);
        deviceCostMap.remove(mac);
        deviceCodeMap.remove(mac);
    }


}
