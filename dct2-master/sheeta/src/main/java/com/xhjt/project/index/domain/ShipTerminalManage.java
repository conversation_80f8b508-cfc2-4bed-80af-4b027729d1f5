package com.xhjt.project.index.domain;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021/12/1 14:53
 */
public class ShipTerminalManage {

    /**
     * id
     */
    private Long id;

    /**
     * sn
     */
    private String sn;

    /**
     * 船名
     */
    private String shipName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 模块型号
     */
    private String moduleModel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 序列号
     */
    private String serialNum;

    /**
     * 软件版本
     */
    private String softVersion;

    /**
     * 授权信息
     */
    private String authorInformation;

    /**
     * 连接状态(0:断开 1：连接)
     */
    private Integer connectStatus;

    /**
     * cpu
     */
    private Double cpu;

    /**
     * 内存
     */
    private Double memory;

    /**
     * 硬盘
     */
    private Double hardDisk;

    /**
     * WAN IP
     */
    private String wanIp;

    /**
     * LAN IP
     */
    private String lanIp;

    /**
     * 运行时长
     */
    private String runTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getModuleModel() {
        return moduleModel;
    }

    public void setModuleModel(String moduleModel) {
        this.moduleModel = moduleModel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(String serialNum) {
        this.serialNum = serialNum;
    }

    public String getSoftVersion() {
        return softVersion;
    }

    public void setSoftVersion(String softVersion) {
        this.softVersion = softVersion;
    }

    public String getAuthorInformation() {
        return authorInformation;
    }

    public void setAuthorInformation(String authorInformation) {
        this.authorInformation = authorInformation;
    }

    public Integer getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(Integer connectStatus) {
        this.connectStatus = connectStatus;
    }

    public Double getCpu() {
        return cpu;
    }

    public void setCpu(Double cpu) {
        this.cpu = cpu;
    }

    public Double getMemory() {
        return memory;
    }

    public void setMemory(Double memory) {
        this.memory = memory;
    }

    public Double getHardDisk() {
        return hardDisk;
    }

    public void setHardDisk(Double hardDisk) {
        this.hardDisk = hardDisk;
    }

    public String getWanIp() {
        return wanIp;
    }

    public void setWanIp(String wanIp) {
        this.wanIp = wanIp;
    }

    public String getLanIp() {
        return lanIp;
    }

    public void setLanIp(String lanIp) {
        this.lanIp = lanIp;
    }

    public String getRunTime() {
        return runTime;
    }

    public void setRunTime(String runTime) {
        this.runTime = runTime;
    }
}
