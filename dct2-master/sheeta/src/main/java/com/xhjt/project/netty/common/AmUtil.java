package com.xhjt.project.netty.common;

import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.AcquisitionMiddlewareService;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.netty.domain.AmProtocol;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 采集中间件--缓存工具类
 *
 * <AUTHOR>
 */
public class AmUtil {

    protected static Logger logger = LoggerFactory.getLogger(AmUtil.class);

    /**
     * 采集中间件
     */
    private static Map<String, AcquisitionMiddleware> amMap = new ConcurrentHashMap<>();

    /**
     * TCP初始连接时，通过IP和PORT获取采集中间件信息，放入缓存
     *
     * @param ip
     * @param port
     */
    public static void addAm(String ip, Integer port) {
        AcquisitionMiddlewareService amService = SpringUtils.getBean(AcquisitionMiddlewareService.class);
        AcquisitionMiddleware am = amService.changeConnectStatus(ip, port, 1);
        am.setEnable(2);
        DeviceService deviceService = SpringUtils.getBean(DeviceService.class);
        //同步修改设备得状态
        if (am != null && StringUtils.isNotBlank(am.getMac())){
            DeviceEntity device = new DeviceEntity();
            device.setMac(am.getMac());
            device.setConnectType(0);//采集终端
            List<DeviceEntity> deviceEntitys = deviceService.selectDeviceList(device);
            if (deviceEntitys!=null && deviceEntitys.size()>0){
                for (DeviceEntity deviceEntity:deviceEntitys) {
                    if (deviceEntity != null) {
                        deviceEntity.setConnectStatus(1);
                        deviceService.updateDevice(deviceEntity);
                    }
                }
            }
        }
        amMap.put(getAmKey(ip, port), am);
    }

    public static AcquisitionMiddleware getAm(String ip, Integer port) {
        if (StringUtils.isBlank(ip) || port == null) {
            return null;
        }

        return amMap.get(getAmKey(ip, port));
    }

    public static void removeAm(String ip, Integer port) {
        AcquisitionMiddlewareService amService = SpringUtils.getBean(AcquisitionMiddlewareService.class);
        AcquisitionMiddleware am = amService.changeConnectStatus(ip, port, 0);
        DeviceService deviceService = SpringUtils.getBean(DeviceService.class);
        //同步修改设备得状态
        if (am!=null && StringUtils.isNotBlank(am.getMac())){
            DeviceEntity device = new DeviceEntity();
            device.setMac(am.getMac());
            device.setConnectType(0);//采集终端
            List<DeviceEntity> deviceEntitys = deviceService.selectDeviceList(device);
            if (deviceEntitys!=null && deviceEntitys.size()>0){
                for (DeviceEntity deviceEntity:deviceEntitys) {
                    deviceEntity.setConnectStatus(0);
                    deviceService.updateDevice(deviceEntity);
                }
            }
        }

        amMap.remove(getAmKey(ip, port));
        //同步到岸端

        // 删除部分无效数据
//        amService.delete4Invalid();
    }

    /**
     * 更新 采集中间件的信息
     * 只有接收到信息后，才知道MAC、类型、名称等信息，通过MAC查找绑定的设备信息
     *
     * @param ip
     * @param port
     * @param amProtocol
     * @return
     */
    public static AcquisitionMiddleware renewAm(String ip, Integer port, AmProtocol amProtocol) {
        boolean flag = false;
        AcquisitionMiddlewareService amService = SpringUtils.getBean(AcquisitionMiddlewareService.class);
        AcquisitionMiddleware am = getAm(ip, port);

        if (am == null) {
            am = new AcquisitionMiddleware();
        }

        DeviceService deviceService = SpringUtils.getBean(DeviceService.class);
        DeviceEntity device1 = new DeviceEntity();
        device1.setMac(amProtocol.getMac());
        device1.setConnectType(0);//采集终端
        List<DeviceEntity> deviceEntitys = deviceService.selectDeviceList(device1);
        if (deviceEntitys != null && deviceEntitys.size()> 0){
            for (DeviceEntity device:deviceEntitys) {
                if (device.getEnable()==1){
                    flag = true;
                }
                device.setConnectStatus(am.getConnectStatus());
                deviceService.updateDevice(device);
            }
        }
        if (!amProtocol.getMac().equals(am.getMac())) {
            // 先删除MAC原有的绑定IP、PORT
            amService.deleteAcquisitionMiddlewareByMac(amProtocol.getMac());
            //同步到岸端-删除
            am.setCode(amProtocol.getName());
            am.setMac(amProtocol.getMac());
            amService.addAcquisitionMiddleware(am);
            //同步到岸端-新增
            amService.syncNewShore(am, 2);
        }
        if (flag){
            //该采集终端只要有一个设备启用，那么该采集终端则为正常接收状态
            am.setEnable(1);
        }else {
            //该采集终端的设备都未开启状态，则禁用
            am.setEnable(0);
        }
//        am.setEnable(device == null ? 0 : device.getEnable());
//        am.setTransferStatus(device == null ? 0 : device.getTransferStatus());//加入传输状态
        amMap.put(getAmKey(ip, port), am);

        return am;
    }

    /**
     * 修改设备启用状态时，同步更新缓存中状态
     *
     * @param device
     */
    public static void renewAmByEnable(DeviceEntity device) {
        boolean flag = false;
        AcquisitionMiddleware am = getAm(device.getIp(), device.getPort());
        if (am == null) {
            return;
        }
        DeviceService deviceService = SpringUtils.getBean(DeviceService.class);
        DeviceEntity device1 = new DeviceEntity();
        device1.setMac(device.getMac());
        device1.setConnectType(0);//采集终端
        List<DeviceEntity> deviceEntitys = deviceService.selectDeviceList(device1);
        if (deviceEntitys != null && deviceEntitys.size()> 0){
            for (DeviceEntity device2:deviceEntitys) {
                if (device2.getEnable()==1){
                    flag = true;
                }
            }
        }
        if (flag){
            //该采集终端只要有一个设备启用，那么该采集终端则为正常接收状态
            am.setEnable(1);
        }else {
            //该采集终端的设备都未开启状态，则禁用
            am.setEnable(0);
        }
//        am.setTransferStatus(device.getTransferStatus());//存储
//        am.setType(device.getType());

        amMap.put(getAmKey(device.getIp(), device.getPort()), am);

    }

    /**
     * 新增设备，修改设备时更新
     *
     * @param am
     */
    public static void renewAmByDevice(AcquisitionMiddleware am) {
         if (am == null) {
            return;
        }
        amMap.put(getAmKey(am.getIp(), am.getPort()), am);
         logger.info("当前采集终端-{}",amMap.toString());
    }


    public static void cleanMap() {
        amMap.clear();
    }

    private static String getAmKey(String ip, Integer port) {
        return "Am-" + ip + ":" + port;
    }
}
