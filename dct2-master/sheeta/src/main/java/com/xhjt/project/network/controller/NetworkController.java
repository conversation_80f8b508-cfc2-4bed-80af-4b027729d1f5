package com.xhjt.project.network.controller;

import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.utils.IpUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.network.domain.NetworkConfig;
import com.xhjt.project.network.service.NetworkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/network")
public class NetworkController {


    @Autowired
    private NetworkService networkService;

    /**
     * 获取网卡名称
     */
    @GetMapping("/getNetConfig")
    @PreAuthorize("@ss.hasPermi('system:network:list')")
    public AjaxResult getNetConfig() {
        return AjaxResult.success(networkService.getNetConfig());
    }

    /**
     * 获取网卡名称
     */
    @PreAuthorize("@ss.hasPermi('system:network:edit')")
    @Log(title = "网卡配置", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult updateNetwork(@Validated @RequestBody NetworkConfig networkConfig) throws Exception {
        if ("STATIC".equals(networkConfig.getIp()) && StringUtils.isEmpty(networkConfig.getIp())) {
            throw new Exception("ip不能为空");
        }
        if ("STATIC".equals(networkConfig.getIp()) && StringUtils.isEmpty(networkConfig.getNetmask())) {
            throw new Exception("子网掩码不能为空");
        }
        if (StringUtils.isNotEmpty(networkConfig.getIp()) && !networkService.ipCheck(networkConfig.getIp()) ) {
            throw new Exception("ip格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getNetmask()) && !networkService.ipCheck(networkConfig.getNetmask())) {
            throw new Exception("掩码格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getGateway()) && !networkService.ipCheck(networkConfig.getGateway())) {
            throw new Exception("网关格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getDns1()) && !networkService.ipCheck(networkConfig.getDns1())) {
            throw new Exception("dns1 ip格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getDns2()) && !networkService.ipCheck(networkConfig.getDns2())) {
            throw new Exception("dns2 ip格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getVisit1()) && !IpUtil.isIpMask(networkConfig.getVisit1())) {
            throw new Exception("访问网段1格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getVisit2()) && !IpUtil.isIpMask(networkConfig.getVisit2())) {
            throw new Exception("访问网段2格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getVisit3()) && !IpUtil.isIpMask(networkConfig.getVisit3())) {
            throw new Exception("访问网段3格式不正确");
        }

        return AjaxResult.success(networkService.updateNetwork(networkConfig));
    }
}
