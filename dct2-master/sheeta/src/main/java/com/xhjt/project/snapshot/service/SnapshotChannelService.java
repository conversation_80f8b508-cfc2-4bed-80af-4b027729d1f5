package com.xhjt.project.snapshot.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.ListUtil;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.project.device.domain.TransferSnapshotVo;
import com.xhjt.project.netty.service.StoreService;
import com.xhjt.project.snapshot.mapper.SnapshotChannelMapper;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 通道配置 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotChannelService {
    private static final Logger logger = LoggerFactory.getLogger(SnapshotChannelService.class);

    @Autowired
    private SnapshotChannelMapper snapshotChannelMapper;
    @Autowired
    private StoreService storeService;
    @Autowired
    private SnapshotHandlerService snapshotHandlerService;

    public List<SnapshotChannelEntity> selectSnapshotChannelList(SnapshotChannelEntity snapshotChannelEntity) {
        return snapshotChannelMapper.selectSnapshotChannelList(snapshotChannelEntity);
    }

    public List<SnapshotChannelEntity> queryUnboundList() {
        List<SnapshotChannelEntity> queryList = snapshotChannelMapper.selectSnapshotChannelList(new SnapshotChannelEntity());

        return queryList.stream()
                .filter(channel -> SnapshotUtil.getTransfer(channel.getCode()) == null)
                .collect(Collectors.toList());
    }

    public SnapshotChannelEntity selectSnapshotChannelById(Long id) {
        SnapshotChannelEntity snapshotChannel = new SnapshotChannelEntity();
        snapshotChannel.setId(id);
        return snapshotChannelMapper.selectSnapshotChannel(snapshotChannel);
    }

    public SnapshotChannelEntity selectSnapshotChannelByName(String name) {
        SnapshotChannelEntity snapshotChannel = new SnapshotChannelEntity();
        snapshotChannel.setName(name);
        return snapshotChannelMapper.selectSnapshotChannel(snapshotChannel);
    }

    public SnapshotChannelEntity selectSnapshotChannelByCode(String code) {
        SnapshotChannelEntity snapshotChannel = new SnapshotChannelEntity();
        snapshotChannel.setCode(code);
        return snapshotChannelMapper.selectSnapshotChannel(snapshotChannel);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotChannelEntity addSnapshotChannel(SnapshotChannelEntity snapshotChannel) throws Exception {

        if (selectSnapshotChannelByName(snapshotChannel.getName()) != null) {
            return null;
        }
        snapshotChannel.setCode(getCode());
       int row = snapshotChannelMapper.addSnapshotChannel(snapshotChannel);
       if (row == 0){
           return null;
       }
        return snapshotChannel;
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateSnapshotChannel(SnapshotChannelEntity snapshotChannel) {
        SnapshotChannelEntity channelEntity = selectSnapshotChannelByName(snapshotChannel.getName());
        if (channelEntity != null && !channelEntity.getId().equals(snapshotChannel.getId())) {
            return 0;
        }

        return snapshotChannelMapper.updateSnapshotChannel(snapshotChannel);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotChannelById(Long id) {
        return snapshotChannelMapper.deleteSnapshotChannelById(id);
    }

    /**
     * 批量删除参数信息
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotChannelByIds(Long[] ids) {
        return snapshotChannelMapper.deleteSnapshotChannelByIds(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotChannelByCode(String code) {
        return snapshotChannelMapper.deleteSnapshotChannelByCode(code);
    }

    private String getCode() throws Exception {
        List<SnapshotChannelEntity> list = selectSnapshotChannelList(null);
        List<String> codeList = ListUtil.fetchFieldValuesList(list, "code");

        return Stream.iterate(0, i -> i++)
                .map(i -> getCharAndNum(4))
                .filter(str -> !codeList.contains(str))
                .limit(1)
                .collect(Collectors.joining());
    }

    private String getCharAndNum(int length) {
        StringBuilder val = new StringBuilder();
        Random random = new Random();

        Stream.iterate(0, i -> i + 1).limit(length).forEach(i -> {
            // 字符串或者数字
            if (random.nextInt(2) % 2 == 0) {
                // 取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val.append((char) (choice + random.nextInt(26)));
            } else {
                val.append((random.nextInt(10)));
            }
        });

        return val.toString();
    }

    /**
     * 更新到岸端
     * @param channelEntity
     * @param action
     */
    public void sync2Shore(SnapshotChannelEntity channelEntity, int action) {
        SnapshotChannelEntity entity = new SnapshotChannelEntity();
        entity.setCode(channelEntity.getCode());
        entity.setName(channelEntity.getName());
        entity.setAddress(channelEntity.getAddress());
        entity.setStorageTime(channelEntity.getStorageTime());

        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(entity), action, "snapshotChannel");

        KafkaMessage kafkaMessage = new KafkaMessage(channelEntity.getCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }


    /**
     * 更新岸端传来的数据
     *
     * @param syncEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized void handleBySync(SyncEntity syncEntity) {
        SnapshotChannelEntity syncChannel = JSON.parseObject(syncEntity.getJsonObject(), SnapshotChannelEntity.class);
        SnapshotChannelEntity snapshotChannelEntity = selectSnapshotChannelByCode(syncChannel.getCode());

        if (syncEntity.getAction() == 1 && snapshotChannelEntity == null) {
            syncChannel.setCreateBy("sync");
           int row = snapshotChannelMapper.addSnapshotChannel(syncChannel);
            try {
                //岸端第一次添加通道，直接去获取摄像头截屏
                if (row>0){
                    SnapshotUtil.addAllInfo();
                    snapshotHandlerService.start(syncChannel.getCode());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (syncEntity.getAction() == 2 && snapshotChannelEntity != null) {

            snapshotChannelEntity.setName(syncChannel.getName());
            snapshotChannelEntity.setAddress(syncChannel.getAddress());
            snapshotChannelEntity.setStorageTime(syncChannel.getStorageTime());
            snapshotChannelEntity.setUpdateBy("sync");

            snapshotChannelMapper.updateSnapshotChannel(snapshotChannelEntity);

        } else if (syncEntity.getAction() == 3 && snapshotChannelEntity != null) {
            deleteSnapshotChannelById(snapshotChannelEntity.getId());
        }
    }
    /**
     * 更新岸端传来的数据-new
     *
     * @param syncChannel
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized void handleNewBySync(SnapshotChannelEntity syncChannel,int action) {
        SnapshotChannelEntity snapshotChannelEntity = selectSnapshotChannelByCode(syncChannel.getCode());

        if (action == 1 && snapshotChannelEntity == null) {
            syncChannel.setCreateBy("sync");
            int row = snapshotChannelMapper.addSnapshotChannel(syncChannel);
            try {
                //岸端第一次添加通道，直接去获取摄像头截屏
                if (row>0){
                    SnapshotUtil.addAllInfo();
                    snapshotHandlerService.start(syncChannel.getCode());
                    logger.info("岸端快照通道新增成功并开启截屏---{}",syncChannel.getCode());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (action == 2 && snapshotChannelEntity != null) {

            snapshotChannelEntity.setName(syncChannel.getName());
            snapshotChannelEntity.setAddress(syncChannel.getAddress());
            snapshotChannelEntity.setStorageTime(syncChannel.getStorageTime());
            snapshotChannelEntity.setUpdateBy("sync");

            int row = snapshotChannelMapper.updateSnapshotChannel(snapshotChannelEntity);
            if (row>0){
                logger.info("岸端快照通道修改成功---{}",snapshotChannelEntity.getCode());
            }
        } else if (action == 3 && snapshotChannelEntity != null) {
            deleteSnapshotChannelById(snapshotChannelEntity.getId());
        }
    }
    /**
     * 更新到岸端
     * @param transferSnapshotVo
     * @param action
     */
    public void syncNewShore(TransferSnapshotVo transferSnapshotVo, int action) {
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(transferSnapshotVo), action, "transferSnapshot");
        KafkaMessage kafkaMessage = new KafkaMessage(transferSnapshotVo.getSnapshotChannelEntity().getCode(), JSONObject.toJSONString(syncEntity), transferSnapshotVo.getSnapshotTransferEntity().getCost(), System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }

    public void setConnectStatus(SnapshotTransferEntity snapshotTransfer){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateS1 = df.format(new Date());

        try {
            if (snapshotTransfer.getOperateTime()!=null){
                String dateS2 = DateUtils.parseTimeToDate(snapshotTransfer.getOperateTime(),"yyyy-MM-dd HH:mm:ss");
                Date date1 = df.parse(dateS1);
                Date date2 = df.parse(dateS2);
                long diff = date1.getTime() - date2.getTime();
                long seconds = diff / 1000;
                if (seconds-(snapshotTransfer.getCompartment()+60) >0){
                    snapshotTransfer.setConnectStatus("0");
                }else{
                    snapshotTransfer.setConnectStatus("1");
                }
            }

            if (snapshotTransfer.getCompartment() == null) {
                snapshotTransfer.setConnectStatus("0");
            }

        } catch (ParseException e) {
            snapshotTransfer.setConnectStatus("0");
        }
    }
}
