package com.xhjt.project.network.domain;


public class NetworkConfig {

    /**
     * 网口名称
     */
    private String name;

    /**
     * 网口类型   WAN  LAN
     */
    private String type;

    /**
     * 配置模式
     */
    private String mode;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 子网掩码
     */
    private String netmask;

    /**
     * 网关
     */
    private String gateway;

    private String dns1;

    private String dns2;

    /**
     * 访问网段
     */
    private String visit1;

    private String visit2;

    private String visit3;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getNetmask() {
        return netmask;
    }

    public void setNetmask(String netmask) {
        this.netmask = netmask;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getDns1() {
        return dns1;
    }

    public void setDns1(String dns1) {
        this.dns1 = dns1;
    }

    public String getDns2() {
        return dns2;
    }

    public void setDns2(String dns2) {
        this.dns2 = dns2;
    }

    public String getVisit1() {
        return visit1;
    }

    public void setVisit1(String visit1) {
        this.visit1 = visit1;
    }

    public String getVisit2() {
        return visit2;
    }

    public void setVisit2(String visit2) {
        this.visit2 = visit2;
    }

    public String getVisit3() {
        return visit3;
    }

    public void setVisit3(String visit3) {
        this.visit3 = visit3;
    }
}
