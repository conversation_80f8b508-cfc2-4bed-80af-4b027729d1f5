package com.xhjt.project.index.service.impl;

import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.UptimeUtil;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.index.domain.DataList;
import com.xhjt.project.index.domain.ShipTerminalManage;
import com.xhjt.project.index.domain.SysOperLog2;
import com.xhjt.project.index.mapper.AdviceEquipmentAccessMapper;
import com.xhjt.project.index.mapper.SysRunStatusMapper;
import com.xhjt.project.index.mapper.TransmissionSituationMapper;
import com.xhjt.project.index.service.IndexService;
import com.xhjt.project.monitor.domain.Server;
import com.xhjt.project.monitor.domain.server.Cpu;
import com.xhjt.project.monitor.domain.server.Mem;
import com.xhjt.project.monitor.domain.server.Sys;
import com.xhjt.project.netty.handler.ChannelManagerHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/12/1 17:13
 */
@Service
public class IndexServiceImpl implements IndexService {
    @Autowired
    private AdviceEquipmentAccessMapper adviceEquipmentAccessMapper;

    @Autowired
    private TransmissionSituationMapper transmissionSituationMapper;

    @Autowired
    private SysRunStatusMapper sysRunStatusMapper;

    private static String osName = System.getProperty("os.name");

    protected Logger logger = LoggerFactory.getLogger(ChannelManagerHandler.class);

    /**
     * 硬盘使用率
     */
    @Override
    public Double getDeskUsage() throws IOException {
        double totalHD = 0;
        double usedHD = 0;
        if (osName.toLowerCase().contains("windows")
                || osName.toLowerCase().contains("win")) {
            long allTotal = 0;
            long allFree = 0;
            for (char c = 'A'; c <= 'Z'; c++) {
                String dirName = c + ":/";
                File win = new File(dirName);
                if (win.exists()) {
                    long total = win.getTotalSpace();
                    long free = win.getFreeSpace();
                    allTotal = allTotal + total;
                    allFree = allFree + free;
                }
            }
            Double precent = (1 - allFree * 1.0 / allTotal) * 100;
            BigDecimal b1 = new BigDecimal(precent);
            precent = b1.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            return precent;
        } else {
            Runtime rt = Runtime.getRuntime();
            Process p = null;// df -hl 查看硬盘空间
            p = rt.exec("df -hl /home");
            BufferedReader in = null;
            try {
                in = new BufferedReader(new InputStreamReader(p.getInputStream()));
                String str = null;
                String[] strArray = null;
                while ((str = in.readLine()) != null) {
                    int m = 0;
                    strArray = str.split(" ");
                    for (String tmp : strArray) {
                        if (tmp.trim().length() == 0)
                            continue;
                        ++m;
                        if (tmp.indexOf("G") != -1) {
                            if (m == 2) {
                                if (!tmp.equals("") && !tmp.equals("0"))
                                    totalHD += Double.parseDouble(tmp.substring(0, tmp.length() - 1)) * 1024;
                            }
                            if (m == 3) {
                                if (!tmp.equals("none") && !tmp.equals("0"))
                                    usedHD += Double.parseDouble(tmp.substring(0, tmp.length() - 1)) * 1024;
                            }
                        }
                        if (tmp.indexOf("M") != -1) {
                            if (m == 2) {
                                if (!tmp.equals("") && !tmp.equals("0"))
                                    totalHD += Double.parseDouble(tmp.substring(0, tmp.length() - 1));
                            }
                            if (m == 3) {
                                if (!tmp.equals("none") && !tmp.equals("0"))
                                    usedHD += Double.parseDouble(tmp.substring(0, tmp.length() - 1));
                            }
                        }
                    }
                }
            } catch (Exception e) {

            } finally {
                in.close();
            }
            // 保留2位小数
            double precent = (usedHD / totalHD) * 100;
            BigDecimal b1 = new BigDecimal(precent);
            precent = b1.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            return precent;
        }
    }

    /**
     * 时钟信息
     */
    @Override
    public String getSysDate() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        //返回系统当前时间
        return df.format(new Date());
    }

    /**
     * 运行时长
     */
    @Override
    public String getRunTime() {
        //String cmd = "cat /proc/uptime";
        //String[] cmdA = {"/bin/sh", "-c", cmd};
        //return getUptimeSecond2(UptimeUtil.getCmdResult(cmdA));
        return getUptimeSecond2(UptimeUtil.getRouterUptime());
    }

    /**
     * 设备接入数
     */
    @Override
    public Integer seletctDeviceCount() {
        return adviceEquipmentAccessMapper.seletctDeviceCount();
    }

    /**
     * 设备接入数-总数
     */
    @Override
    public Integer seletctDeviceAllCount() {
        return adviceEquipmentAccessMapper.seletctDeviceAllCount();
    }

    /**
     * 获取设备
     */
    @Override
    public List<Integer> seletctDeviceAllCountList() {
        return adviceEquipmentAccessMapper.seletctDeviceAllCountList();
    }

    /**
     * 设备参数量
     */
    @Override
    public Integer selectDeviceAttributeCount(Integer type) {
        return adviceEquipmentAccessMapper.selectDeviceAttributeCount(type);
    }

    /**
     * 设备参数量-总数
     */
    @Override
    public Integer selectTransferAttributeCount() {
        return adviceEquipmentAccessMapper.selectTransferAttributeCount();
    }

    /**
     * 首页-快照通道数
     */
    @Override
    public List<Integer> snapshotChannelCount() {
        Integer count = 0;
        Integer allCount = 0;
        if (seletctSnapshotAllCount() != null) {
            allCount = seletctSnapshotAllCount();
        }
        List<SnapshotChannelEntity> list = selectSnapshotChannelAllList();
        for (SnapshotChannelEntity snapshotChannelEntity1 : list) {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateS1 = df.format(new Date());
            Long operateTime = null;
            String dateS2 = "";
            if (snapshotChannelEntity1.getOperateTime() != null) {
                operateTime = snapshotChannelEntity1.getOperateTime();
                dateS2 = DateUtils.parseTimeToDate(operateTime, "yyyy-MM-dd HH:mm:ss");
            }
            try {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(dateS2)) {
                    Date date1 = df.parse(dateS1);
                    Date date2 = df.parse(dateS2);
                    long diff = date1.getTime() - date2.getTime();
                    long seconds = diff / 1000;
                    if (seconds - (snapshotChannelEntity1.getCompartment()+60) > 0) {
                        snapshotChannelEntity1.setStatus("0");
                        snapshotChannelEntity1.setTrStatus(0);
                        snapshotChannelEntity1.setTransferStatus(0);
                    } else {
                        count = count + 1;
                    }
                }
                if (snapshotChannelEntity1.getCompartment() == null || org.apache.commons.lang3.StringUtils.isBlank(dateS2)) {
                    snapshotChannelEntity1.setStatus("0");
                    snapshotChannelEntity1.setTrStatus(0);
                    snapshotChannelEntity1.setTransferStatus(0);
                }
            } catch (ParseException e) {
                snapshotChannelEntity1.setStatus("0");
                snapshotChannelEntity1.setTrStatus(0);
                snapshotChannelEntity1.setTransferStatus(0);
                logger.error("日期转义失败");
            }
        }
        List<Integer> result = new ArrayList<Integer>();
        result.add(count);
        result.add(allCount);
        return result;
    }

    /**
     * 快照通道数
     */
    @Override
    public List<SnapshotChannelEntity> selectSnapshotChannelAllList() {
        return adviceEquipmentAccessMapper.selectSnapshotChannelAllList();
    }

    /**
     * 快照通道数-总数
     */
    @Override
    public Integer seletctSnapshotAllCount() {
        return adviceEquipmentAccessMapper.seletctSnapshotAllCount();
    }

    /**
     * 设备采集传输情况
     */
    @Override
    public List<String> getData(String type) {
        Double acquisitionAllCount = transmissionSituationMapper.getAcquisition(type);
        Double acquisitionDeviceCount = transmissionSituationMapper.getAcacquisitionDevice(type);
        Double acquisitionSnapshotCount = transmissionSituationMapper.getAcquisitionSnapshot(type);
        Double transmissionAllCount = transmissionSituationMapper.getTransmission(type);
        Double transmissionDeviceCount = transmissionSituationMapper.getTransmissionDevice(type);
        Double transmissionSnapshotCount = transmissionSituationMapper.getTransmissionSnapshot(type);
        List<String> res = new ArrayList<>();
        res.add(acquisitionAllCount / 1024 / 1024 + "");
        res.add(acquisitionDeviceCount / 1024 / 1024 + "");
        res.add(acquisitionSnapshotCount / 1024 / 1024 + "");
        res.add(transmissionAllCount / 1024 / 1024 + "");
        res.add(transmissionDeviceCount / 1024 / 1024 + "");
        res.add(transmissionSnapshotCount / 1024 / 1024 + "");
        return res;
    }

    /**
     * 平均传输速率-设备
     */
    @Override
    public List<DataList> getDeviceDataList(String type) {
        List<DataList> lists = transmissionSituationMapper.getDeviceDataList(type);
        for (DataList list : lists) {
            double t = Double.parseDouble(list.getCharacterLength()) / 1024 / 1024;
            list.setCharacterLength(t + "");
        }
        return lists;
    }

    /**
     * 平均传输速率-快照
     */
    @Override
    public List<DataList> getSnapshotDataList(String type) {
        List<DataList> lists = transmissionSituationMapper.getSnapshotDataList(type);
        for (DataList list : lists) {
            double t = Double.parseDouble(list.getCharacterLength()) / 1024 / 1024;
            list.setCharacterLength(t + "");
        }
        return lists;
    }

    /**
     * 设备运行状态
     */
    public List<SysOperLog2> sysRunStatus() {
        List<SysOperLog2> lists = sysRunStatusMapper.sysRunStatus();
        return lists;
    }

    @Override
    public ShipTerminalManage getShipTerminalManage() throws IOException {
        Server server = new Server();
        server.copyTo();
        ShipTerminalManage shipTerminalManage = new ShipTerminalManage();
        shipTerminalManage.setModuleModel("moduleModel");
        shipTerminalManage.setSerialNum("123456");
        shipTerminalManage.setSoftVersion("xh-tm001 v0001r0003");
        shipTerminalManage.setAuthorInformation("授权信息");
        Cpu cpu = server.getCpu();
        Mem men = server.getMem();
        Sys sys = server.getSys();
        shipTerminalManage.setCpu(Double.parseDouble(String.format("%.2f", (cpu.getSys() + cpu.getUsed()))));
        shipTerminalManage.setMemory(Double.parseDouble(String.format("%.2f", men.getUsage())));
        shipTerminalManage.setHardDisk(Double.parseDouble(String.format("%.2f", getDeskUsage())));
        shipTerminalManage.setWanIp(getHostIp());
        shipTerminalManage.setLanIp(getHostIp());
        Long time = Long.parseLong(getRunTime());
        shipTerminalManage.setRunTime(formatSecondTime(time));
        return shipTerminalManage;
    }

    public static String getUptimeSecond2(String str) {
        String result = "0";

        if (StringUtils.isNotEmpty(str)) {
            if (str.contains(" ")) {
                String[] re = str.split(" ");

                if (re.length > 0) {
                    String first = re[0];
                    if (first.contains(".")) {
                        result = first.substring(0, first.indexOf("."));
                    } else {
                        result = first;
                    }
                }
            } else {
                if (str.contains(".")) {
                    result = str.substring(0, str.indexOf("."));
                } else {
                    result = str;
                }
            }
        }
        return result;
    }

    public static String formatSecondTime(Long time) {
        int day = (int) (time / 24 / 60 / 60);
        time = time - day * 24 * 60 * 60;
        int hour = (int) (time / 60 / 60);
        time = time - hour * 60 * 60;
        int min = (int) (time / 60);
        return day + "天" + hour + "时" + min + "分";
    }

    private static String getHostIp() {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
                Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress ip = (InetAddress) addresses.nextElement();
                    if (ip != null
                            && ip instanceof Inet4Address
                            && !ip.isLoopbackAddress() //loopback地址即本机地址，IPv4的loopback范围是********* ~ ***************
                            && ip.getHostAddress().indexOf(":") == -1) {
                        return ip.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;


    }
}
