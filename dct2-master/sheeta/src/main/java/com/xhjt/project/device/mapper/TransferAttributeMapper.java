package com.xhjt.project.device.mapper;



import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * 设备属性管理 数据层
 *
 * <AUTHOR>
 */
public interface TransferAttributeMapper {
    /**
     * 查询设备信息
     *
     * @param transferAttribute 设备信息
     * @return 设备信息
     */
    public TransferAttributeEntity selectTransferAttribute(TransferAttributeEntity transferAttribute);

    /**
     * 查询设备列表
     *
     * @param transferAttribute 设备信息
     * @return 设备集合
     */
    public List<TransferAttributeEntity> selectTransferAttributeList(TransferAttributeEntity transferAttribute);

    /**
     * 新增设备
     *
     * @param transferAttribute 设备信息
     * @return 结果
     */
    public int addTransferAttribute(TransferAttributeEntity transferAttribute);

    /**
     * 修改设备
     *
     * @param transferAttribute 设备信息
     * @return 结果
     */
    public int updateTransferAttribute(TransferAttributeEntity transferAttribute);

    /**
     * 删除设备
     *
     * @param transferAttributeId 参数ID
     * @return 结果
     */
    public int deleteTransferAttributeById(Long transferAttributeId);

    /**
     * 批量删除参数信息
     *
     * @param transferAttributeIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteTransferAttributeByIds(Long[] transferAttributeIds);


    /**
     * 删除
     *
     * @param deviceCode 参数mac
     * @return 结果
     */
    public int deleteTransferAttributeByCode(String deviceCode);


}
