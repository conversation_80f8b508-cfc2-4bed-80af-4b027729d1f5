package com.xhjt.project.device.mapper;


import com.xhjt.project.device.domain.DataStatistics;

import java.util.List;

/**
 * 采集-传输数据收集记录表
 *
 * <AUTHOR>
 */
public interface DataStatisticsMapper {

    /**
     * 查询记录列表
     *
     * @param dataStatistics
     * @return
     */
    public List<DataStatistics> selectDataStatisticsList(DataStatistics dataStatistics);

    /**
     * 新增
     *
     * @param dataStatistics
     * @return 结果
     */
    public int addDataStatistics(DataStatistics dataStatistics);

    /**
     * 删除
     *
     * @param id 参数ID
     * @return 结果
     */
    public int deleteDataStatisticsById(Long id);

    /**
     * 批量删除参数信息
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    public int deleteDataStatisticsByIds(Long[] ids);

    public void deleteDataStatisticsByTime(Long time);
}