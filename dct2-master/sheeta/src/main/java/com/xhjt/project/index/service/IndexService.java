package com.xhjt.project.index.service;


import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.project.index.domain.DataList;
import com.xhjt.project.index.domain.ShipTerminalManage;
import com.xhjt.project.index.domain.SysOperLog2;

import java.io.IOException;
import java.util.List;

public interface IndexService {
    /**
     * 硬盘使用率
     */
    Double getDeskUsage() throws IOException;

    /**
     * 时钟信息
     */
    String getSysDate();

    /**
     * 运行时长
     */
    String getRunTime();

    /**
     * 设备接入数
     */
    Integer seletctDeviceCount();

    /**
     * 设备接入数-总数
     */
    Integer seletctDeviceAllCount();

    /**
     * 获取设备
     */
    List<Integer> seletctDeviceAllCountList();

    /**
     * 设备参数量
     */
    Integer selectDeviceAttributeCount(Integer type);

    /**
     * 设备参数量-总数
     */
    Integer selectTransferAttributeCount();

    /**
     * 首页-快照通道数
     */
    List<Integer> snapshotChannelCount();

    /**
     * 快照通道数
     */
    List<SnapshotChannelEntity> selectSnapshotChannelAllList();

    /**
     * 快照通道数-总数
     */
    Integer seletctSnapshotAllCount();

    /**
     * 设备采集传输情况
     */
    List<String> getData(String type);

    /**
     * 平均传输速率-设备
     */
    List<DataList> getDeviceDataList(String type);

    /**
     * 平均传输速率-快照
     */
    List<DataList> getSnapshotDataList(String type);

    /**
     * 设备运行状态
     */
    List<SysOperLog2> sysRunStatus();

    ShipTerminalManage getShipTerminalManage() throws IOException;
}
