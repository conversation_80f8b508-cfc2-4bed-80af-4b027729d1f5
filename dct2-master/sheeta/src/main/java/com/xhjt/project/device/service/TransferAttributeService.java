package com.xhjt.project.device.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.device.DeviceAttributeEntity;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.mapper.TransferAttributeMapper;
import com.xhjt.project.netty.service.StoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class TransferAttributeService {

    protected static Logger logger = LoggerFactory.getLogger(TransferAttributeService.class);

    @Autowired
    private TransferAttributeMapper transferAttributeMapper;

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private DeviceAttributeService deviceAttributeService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 查询设备信息
     *
     * @param transferAttributeId 设备ID
     * @return 设备信息
     */
    @Transactional(rollbackFor = Exception.class)
    public TransferAttributeEntity selectTransferAttributeById(Long transferAttributeId) {
        TransferAttributeEntity transferAttribute = new TransferAttributeEntity();
        transferAttribute.setId(transferAttributeId);
        return transferAttributeMapper.selectTransferAttribute(transferAttribute);
    }

    /**
     * 查询设备列表
     *
     * @param transferAttribute 设备信息
     * @return 设备集合
     */
    @DataScope(deptAlias = "d")
    @Transactional(rollbackFor = Exception.class)
    public List<TransferAttributeEntity> selectTransferAttributeList(TransferAttributeEntity transferAttribute) {
        return transferAttributeMapper.selectTransferAttributeList(transferAttribute);
    }

    /**
     * 查询设备列表
     *
     * @param deviceCode 设备信息
     * @return 设备集合
     */
    @DataScope(deptAlias = "d")
    @Transactional(rollbackFor = Exception.class)
    public List<TransferAttributeEntity> selectListByCode(String deviceCode) {
        TransferAttributeEntity transferAttribute = new TransferAttributeEntity();
        transferAttribute.setDeviceCode(deviceCode);
        return transferAttributeMapper.selectTransferAttributeList(transferAttribute);
    }

    /**
     * 新增设备
     *
     * @param transferAttribute 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addTransferAttribute(TransferAttributeEntity transferAttribute) {
        return transferAttributeMapper.addTransferAttribute(transferAttribute);
    }

    /**
     * 修改设备
     *
     * @param transferAttribute 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public List<TransferAttributeEntity> updateTransferAttribute(TransferAttributeEntity transferAttribute) {
        List<TransferAttributeEntity> entityList = new ArrayList<>();
        DeviceEntity device = deviceService.selectByCode(transferAttribute.getDeviceCode());
        List<DeviceAttributeEntity> attributes = deviceAttributeService.selectListByType(device.getType());
        Map<String, String> map = attributes.stream().collect(Collectors.toMap(DeviceAttributeEntity::getName, DeviceAttributeEntity::getLabel));
        deleteTransferAttributeByCode(transferAttribute.getDeviceCode());
        Stream.iterate(0, i -> i + 1).limit(transferAttribute.getTransferAttributes().length).forEach(index -> {
            TransferAttributeEntity entity = new TransferAttributeEntity();
            entity.setDeviceCode(transferAttribute.getDeviceCode());
            entity.setName(transferAttribute.getTransferAttributes()[index]);
            entity.setLabel(map.get(transferAttribute.getTransferAttributes()[index]));
            entity.setOrderNum(index + 1);
            entity.setCreateBy(transferAttribute.getUpdateBy());
            entityList.add(entity);
            transferAttributeMapper.addTransferAttribute(entity);
        });

        return entityList;
    }


    /**
     * 更新redis中属性集合
     *
     * @throws Exception
     */
    public List<String> renewAttributes(String deviceCode) {

        List<TransferAttributeEntity> list = selectListByCode(deviceCode);

        List<String> attributes = list.stream().map(TransferAttributeEntity::getName).collect(toList());

        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        valueOperations.set("TRANSFER_ATTRIBUTE_" + deviceCode, JSONObject.toJSONString(attributes));

        return attributes;
    }

    /**
     * 修改设备
     *
     * @param list 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateBySync(List<TransferAttributeEntity> list, String deviceCode) {
        DeviceEntity device = deviceService.selectByCode(deviceCode);

        List<DeviceAttributeEntity> attributes = deviceAttributeService.selectListByType(device.getType());
        Map<String, String> map = attributes.stream().collect(Collectors.toMap(DeviceAttributeEntity::getName, DeviceAttributeEntity::getLabel));

        deleteTransferAttributeByCode(deviceCode);

        list.forEach(entity -> {
            entity.setDeviceCode(deviceCode);
            entity.setLabel(map.get(entity.getName()));
            entity.setCreateBy("sync");

            transferAttributeMapper.addTransferAttribute(entity);
        });

        return list.size();
    }

    /**
     * 删除设备信息
     *
     * @param transferAttributeId 参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteTransferAttributeById(Long transferAttributeId) {
        return transferAttributeMapper.deleteTransferAttributeById(transferAttributeId);
    }

    /**
     * 批量删除参数信息
     *
     * @param transferAttributeIds 需要删除的参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteTransferAttributeByIds(Long[] transferAttributeIds) {
        return transferAttributeMapper.deleteTransferAttributeByIds(transferAttributeIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteTransferAttributeByCode(String deviceCode) {
        return transferAttributeMapper.deleteTransferAttributeByCode(deviceCode);
    }

    /**
     * 同步传输配置到岸上
     *
     * @param transferAttribute
     */
    public void sync2Shore(TransferAttributeEntity transferAttribute) {
        TransferAttributeEntity queryEntity = new TransferAttributeEntity();
        queryEntity.setDeviceCode(transferAttribute.getDeviceCode());
        List<TransferAttributeEntity> sourceList = selectTransferAttributeList(queryEntity);

        List<TransferAttributeEntity> list = sourceList.stream().map(ta -> {
            TransferAttributeEntity entity = new TransferAttributeEntity();
            entity.setName(ta.getName());
            entity.setOrderNum(ta.getOrderNum());
            return entity;
        }).collect(Collectors.toList());

        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(list), 1, "transferAttribute");

        KafkaMessage kafkaMessage = new KafkaMessage(transferAttribute.getDeviceCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 查询设备列表-给定时使用
     *
     * @param deviceCode 设备信息
     * @return 设备集合
     */
    public List<TransferAttributeEntity> selectListByDeCode(String deviceCode) {
        TransferAttributeEntity transferAttribute = new TransferAttributeEntity();
        transferAttribute.setDeviceCode(deviceCode);
        return transferAttributeMapper.selectTransferAttributeList(transferAttribute);
    }

}
