package com.xhjt.project.collect.domain;

/**
 * 采集节点命令
 *
 * <AUTHOR>
 */
public class CmdVo {
    /**
     * 序号
     */
    private Integer id;
    /**
     * 发送命令
     */
    private String sendStr;
    /**
     * 准确的返回数据
     */
    private String backStr;
    /**
     * 接收正确，下一步
     */
    private Integer trueNextId;
    /**
     * 接收错误，下一步
     */
    private Integer falseNextId;

    /**
     * 等待循环次数
     */
    private Integer waitNum;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSendStr() {
        return sendStr;
    }

    public void setSendStr(String sendStr) {
        this.sendStr = sendStr;
    }

    public String getBackStr() {
        return backStr;
    }

    public void setBackStr(String backStr) {
        this.backStr = backStr;
    }

    public Integer getTrueNextId() {
        return trueNextId;
    }

    public void setTrueNextId(Integer trueNextId) {
        this.trueNextId = trueNextId;
    }

    public Integer getFalseNextId() {
        return falseNextId;
    }

    public void setFalseNextId(Integer falseNextId) {
        this.falseNextId = falseNextId;
    }

    public Integer getWaitNum() {
        return waitNum == null ? 100 : waitNum;
    }

    public void setWaitNum(Integer waitNum) {
        this.waitNum = waitNum;
    }
}
