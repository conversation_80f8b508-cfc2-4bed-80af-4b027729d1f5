package com.xhjt.project.collect.mapper;


import com.xhjt.project.collect.domain.CollectNode;

import java.util.List;

/**
 * 采集节点 数据层
 *
 * <AUTHOR>
 */
public interface CollectNodeMapper {
    /**
     * 查询采集节点信息
     *
     * @param collectNode 采集节点信息
     * @return 采集节点信息
     */
    public CollectNode selectCollectNode(CollectNode collectNode);

    /**
     * 查询采集节点列表
     *
     * @param collectNode 采集节点信息
     * @return 采集节点集合
     */
    public List<CollectNode> selectCollectNodeList(CollectNode collectNode);

    /**
     * 新增采集节点
     *
     * @param collectNode 采集节点信息
     * @return 结果
     */
    public int addCollectNode(CollectNode collectNode);

    /**
     * 修改采集节点
     *
     * @param collectNode 采集节点信息
     * @return 结果
     */
    public int updateCollectNode(CollectNode collectNode);

    /**
     * 删除采集节点
     *
     * @param collectNodeId 参数ID
     * @return 结果
     */
    public int deleteCollectNodeById(Long collectNodeId);

    /**
     * 批量删除参数信息
     *
     * @param collectNodeIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteCollectNodeByIds(Long[] collectNodeIds);

}
