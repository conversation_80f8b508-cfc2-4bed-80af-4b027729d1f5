package com.xhjt.project.snapshot.controller;

import com.xhjt.common.exception.job.TaskException;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.domain.TransferSnapshotVo;
import com.xhjt.project.monitor.domain.SysOperLog;
import com.xhjt.project.monitor.service.ISysOperLogService;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotHandlerService;
import com.xhjt.project.snapshot.service.SnapshotTransferService;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 快照传输管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/snapshot/transfer")
public class SnapshotTransferController extends BaseController {

    @Autowired
    private SnapshotTransferService snapshotTransferService;

    @Autowired
    private SnapshotHandlerService snapshotHandlerService;

    @Autowired
    private SnapshotChannelService snapshotChannelService;

    @Autowired
    private ISysOperLogService iSysOperLogService;

    @PreAuthorize("@ss.hasPermi('snapshot:transfer:list')")
    @RequestMapping("/list")
    public TableDataInfo list(SnapshotTransferEntity snapshotTransfer) {
        startPage();
        List<SnapshotTransferEntity> list = snapshotTransferService.selectSnapshotTransferList(snapshotTransfer);
        list.stream().forEach(transferEntity->{
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateS1 = df.format(new Date());


            try {
                if (transferEntity.getOperateTime()!=null){
                    String dateS2 = DateUtils.parseTimeToDate(transferEntity.getOperateTime(),"yyyy-MM-dd HH:mm:ss");
                    Date date1 = df.parse(dateS1);
                    Date date2 = df.parse(dateS2);
                    long diff = date1.getTime() - date2.getTime();
                    long seconds = diff / 1000;
                    if (seconds-(transferEntity.getCompartment()+60)>0){
                        transferEntity.setConnectStatus("0");
//                        transferEntity.setStatus(0);
//                        transferEntity.setTransferStatus(0);
                    }else{
                        transferEntity.setConnectStatus("1");
                    }
                 }

                if (transferEntity.getCompartment() == null) {
                    transferEntity.setConnectStatus("0");
//                    transferEntity.setStatus(0);
//                    transferEntity.setTransferStatus(0);
                }

            } catch (ParseException e) {
                transferEntity.setConnectStatus("0");
//                transferEntity.setStatus(0);
//                transferEntity.setTransferStatus(0);
                logger.error("日期转义失败");
            }
        });
        return getDataTable(list);
    }

    @Log(title = "快照传输", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:export')")
    @GetMapping("/export")
    public AjaxResult export(SnapshotTransferEntity snapshotTransfer) {
        List<SnapshotTransferEntity> list = snapshotTransferService.selectSnapshotTransferList(snapshotTransfer);
        ExcelUtil<SnapshotTransferEntity> util = new ExcelUtil<SnapshotTransferEntity>(SnapshotTransferEntity.class);
        return util.exportExcel(list, "快照传输");
    }

    /**
     * 根据id获取配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(snapshotTransferService.selectSnapshotTransferById(id));
    }

    /**
     * 新增
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SnapshotTransferEntity snapshotTransfer) throws TaskException, SchedulerException {
        snapshotTransfer.setCreateBy(SecurityUtils.getUsername());
        snapshotTransfer.setStatus(1);//默认开启存储通道
        snapshotTransfer.setTransferStatus(1);//默认开启传输通道
        int rows = snapshotTransferService.addSnapshotTransfer(snapshotTransfer);
        if (rows > 0) {
            SnapshotUtil.addAllInfo();
            snapshotTransferService.sync2Shore(snapshotTransfer, 1);
        }
        return toAjax(rows);
    }

    /**
     * 修改配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SnapshotTransferEntity snapshotTransfer) throws TaskException, SchedulerException {
        snapshotTransfer.setUpdateBy(SecurityUtils.getUsername());
        int rows = snapshotTransferService.updateSnapshotTransfer(snapshotTransfer);
        if (rows > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotChannelEntity snapshotChannel = snapshotChannelService.selectSnapshotChannelByCode(snapshotTransfer.getChannelCode());
            TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
            transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
            transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
            snapshotChannelService.syncNewShore(transferSnapshotVo, 2);
         }
        return toAjax(rows);
    }

    /**
     * 删除传输管理
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:remove')")
    @Log(title = "传输删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) throws SchedulerException {
        List<SnapshotTransferEntity> list = Arrays.stream(ids).map(id -> snapshotTransferService.selectSnapshotTransferById(id)).collect(Collectors.toList());

        int rows = snapshotTransferService.deleteSnapshotTransferByIds(ids);

        if (rows > 0) {
            SnapshotUtil.addAllInfo();
            list.forEach(transfer -> snapshotTransferService.sync2Shore(transfer, 3));
        }
        return toAjax(rows);
    }

    /**
     * 定时任务-存储状态修改
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:edit')")
    @PostMapping("/changeStatus/{id}/{status}")
    public AjaxResult changeStatus(@PathVariable Long id, @PathVariable Integer status) throws SchedulerException {
        int row = snapshotTransferService.changeStatus(id, status);
        if (row > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(id);
            SnapshotChannelEntity snapshotChannel = snapshotChannelService.selectSnapshotChannelByCode(snapshotTransfer.getChannelCode());
            TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
            transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
            transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
            snapshotChannelService.syncNewShore(transferSnapshotVo, 2);
            if(status != null && status == 1){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("快照传输'"+snapshotTransfer.getChannelName()+"'(存储状态)启动");
                iSysOperLogService.insertOperlog(sysOperLog);
            }else if(status != null && status == 0){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("快照传输'"+snapshotTransfer.getChannelName()+"'(存储状态)关闭");
                iSysOperLogService.insertOperlog(sysOperLog);
            }
        }
        return toAjax(row);
    }

    /**
     * 定时任务-传输状态修改
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:edit')")
    @PostMapping("/changeTrStatus/{id}/{status}")
    public AjaxResult changeTrStatus(@PathVariable Long id, @PathVariable Integer status) throws SchedulerException {
        int row = snapshotTransferService.changeTrStatus(id, status);
        if (row > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(id);
            SnapshotChannelEntity snapshotChannel = snapshotChannelService.selectSnapshotChannelByCode(snapshotTransfer.getChannelCode());
            TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
            transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
            transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
            snapshotChannelService.syncNewShore(transferSnapshotVo, 2);
            if(status != null && status == 1){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("快照传输'"+snapshotTransfer.getChannelName()+"'(传输状态)启动");
                iSysOperLogService.insertOperlog(sysOperLog);
            }else if(status != null && status == 0){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("快照传输'"+snapshotTransfer.getChannelName()+"'(传输状态)关闭");
                iSysOperLogService.insertOperlog(sysOperLog);
            }
        }
        return toAjax(row);
    }

    /**
     * 获取截屏图片
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:edit')")
    @GetMapping("/screenshot")
    public AjaxResult screenshot(String code){
        SnapshotChannelEntity snapshotTransferEntity = snapshotChannelService.selectSnapshotChannelByCode(code);
        String imagePath = snapshotHandlerService.screenshot(snapshotTransferEntity.getAddress());
        return AjaxResult.success(imagePath);
    }
}
