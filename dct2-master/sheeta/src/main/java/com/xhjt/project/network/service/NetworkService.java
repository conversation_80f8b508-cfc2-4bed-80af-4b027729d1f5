package com.xhjt.project.network.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.ip.IpUtils;
import com.xhjt.dctcore.commoncore.utils.IpUtil;
import com.xhjt.project.network.domain.NetworkConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class NetworkService {
    private static final Logger logger = LoggerFactory.getLogger(NetworkService.class);


    /**
     * 获取网卡配置
     *
     * @return
     */
    public List<NetworkConfig> getNetConfig() {
//        String command = ;
        Reader reader = null;
        BufferedReader inetReader = null;
        InputStream inputStream = null;
        List<String> cmdOutput = new ArrayList();
        List<NetworkConfig> list = new ArrayList();
        String line;
        try {
            Process process = Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", "ls /sys/class/net | grep -v `ls /sys/devices/virtual/net/`"});
            inputStream = process.getInputStream();
            reader = new InputStreamReader(inputStream);
            inetReader = new BufferedReader(reader);

            while ((line = inetReader.readLine()) != null) {
                cmdOutput.add(line);
            }
            logger.info("网卡配置--0000，--- {}", JSONObject.toJSONString(cmdOutput));
            for (String name : cmdOutput) {
                if (StringUtils.isEmpty(name)) {
                    continue;
                }
                NetworkConfig networkConfig = getNetworkConfig(name);
                networkConfig.setName(name);

                list.add(networkConfig);
                logger.info("网卡配置--getNetConfig，--- {}", JSONObject.toJSONString(networkConfig));
            }

            process.waitFor();
        } catch (Exception e) {
            logger.error("get inetnames error--{}", e);
            return list;
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
                if (null != reader) {
                    reader.close();
                }
                if (null != inetReader) {
                    inetReader.close();
                }
            } catch (IOException e) {
                logger.error("close inputstream error.");
            }
        }

        return list;
    }

    public synchronized int updateNetwork(NetworkConfig networkConfig) {
        String command = "";
        try {
            // a. 停止对应网口的网络连接
            // nmcli con down enp2s0
            command = "nmcli con down " + networkConfig.getName();
            Runtime.getRuntime().exec(command);
            Thread.sleep(2000);

//          b. 删除网络连接
//          nmcli con del enp2s0
            command = "nmcli con del " + networkConfig.getName();
            Runtime.getRuntime().exec(command);
            Thread.sleep(2000);

//          c. 新建网络连接
//          nmcli con add con-name enp2s0 ifname enp2s0 type ethernet
            command = "nmcli con add con-name " + networkConfig.getName() + " ifname " + networkConfig.getName() + " type ethernet";
            Runtime.getRuntime().exec(command);
            Thread.sleep(2000);

//          d. 配置新的IP信息
//          根据的不同的配置方式下面单独说明
            if ("WAN".equals(networkConfig.getType())) {
                exec4Wan(networkConfig);
            } else if ("LAN".equals(networkConfig.getType())) {
                exec4Lan(networkConfig);
            }

//          e. 启用新的网络连接
//          nmcli con up enp2s0
            command = "nmcli con up " + networkConfig.getName();
            logger.info("网卡配置，---重启--{}", command);
            Runtime.getRuntime().exec(command);
            Thread.sleep(2000);

        } catch (Exception e) {
            logger.error("---配置网卡，出错----{}---", e);
            return 0;
        }
        return 1;
    }

    public void exec4Wan(NetworkConfig networkConfig) throws Exception {
        String command = "";
//        a. 自动获取IP
//        nmcli con mod enp1s0 connection.autoconnect yes ipv4.method auto
        if ("DHCP".equals(networkConfig.getMode())) {
            command = "nmcli con mod " + networkConfig.getName() + " connection.autoconnect yes ipv4.method auto";
            Runtime.getRuntime().exec(command);
            Thread.sleep(2000);
        }

//        b.  手动设置IP
//        nmcli con mod enp1s0 connection.autoconnect yes ipv4.address *************/24 ipv4.gateway ************ ipv4.method manual
//        子网掩码采用长度的方式表达，一定要设置网关
//       如果有设置DNS nmcli con mod enp1s0 ipv4.dns "*********** ***********"
        if ("STATIC".equals(networkConfig.getMode())) {
            command = "nmcli con mod " + networkConfig.getName() + " connection.autoconnect yes ipv4.address " + networkConfig.getIp() + "/" + IpUtil.getMaskNum(networkConfig.getNetmask()) + " ipv4.gateway " + networkConfig.getGateway() + " ipv4.method manual";
            Runtime.getRuntime().exec(command);
            Thread.sleep(2000);
        }

//        如果有设置DNS， nmcli con mod enp1s0 ipv4.dns "*********** ***********"
//        其中 *********** 和 *********** 为要设置的DNS，多个dns值之间用空格隔开
        if (StringUtils.isNotEmpty(networkConfig.getDns1()) || StringUtils.isNotEmpty(networkConfig.getDns2())) {
            String dnss = (StringUtils.isEmpty(networkConfig.getDns1()) ? "" : networkConfig.getDns1()) + " " + (StringUtils.isEmpty(networkConfig.getDns2()) ? "" : networkConfig.getDns2());
            command = "nmcli con mod " + networkConfig.getName() + " ipv4.dns \"" + dnss.trim() + "\"";
            String[] s = new String[]{"/bin/sh", "-c", command};
            logger.info("网卡配置，---7777写配置--{}", command);
            Runtime.getRuntime().exec(s);
            Thread.sleep(2000);
        }
    }

    public void exec4Lan(NetworkConfig networkConfig) throws Exception {
//      LAN口固定为手动设置模式
        if (!"STATIC".equals(networkConfig.getMode())) {
            return;
        }
        String command = "";

//      a. 设置新的IP nmcli con mod enp2s0 connection.autoconnect yes ipv4.addr ************/24 ipv4.method manual
//      子网掩码用长度的表达方式，注意不能在这里设置网关
        command = "nmcli con mod " + networkConfig.getName() + " connection.autoconnect yes ipv4.addr " + networkConfig.getIp() + "/" + IpUtil.getMaskNum(networkConfig.getNetmask()) + " ipv4.method manual";
        Runtime.getRuntime().exec(command);
        Thread.sleep(2000);

//      b. 设置访问网段 nmcli con mod enp2s0 +ipv4.routes "*************/24 **********"
//      其中 *************/24为访问的网段地址和掩码长度，********** 为LAN口新IP的网关
//      如果有多条访问网段信息，则多次输入  ；如果没有那么不配置LAN 口网关
        if (StringUtils.isNotEmpty(networkConfig.getVisit1())) {
            command = "nmcli con mod " + networkConfig.getName() + " +ipv4.routes \"" + networkConfig.getVisit1() + " " + networkConfig.getGateway() + "\"";
            String[] s = new String[]{"/bin/sh", "-c", command};
            logger.info("网卡配置，---88881写配置1--{}", command);
            Runtime.getRuntime().exec(s);
            Thread.sleep(2000);
        }
        if (StringUtils.isNotEmpty(networkConfig.getVisit2())) {
            command = "nmcli con mod " + networkConfig.getName() + " +ipv4.routes \"" + networkConfig.getVisit2() + " " + networkConfig.getGateway() + "\"";
            String[] s = new String[]{"/bin/sh", "-c", command};
            logger.info("网卡配置，---88882写配置2--{}", command);
            Runtime.getRuntime().exec(s);
            Thread.sleep(2000);
        }
        if (StringUtils.isNotEmpty(networkConfig.getVisit3())) {
            command = "nmcli con mod " + networkConfig.getName() + " +ipv4.routes \"" + networkConfig.getVisit3() + " " + networkConfig.getGateway() + "\"";
            String[] s = new String[]{"/bin/sh", "-c", command};
            logger.info("网卡配置，---88883写配置3--{}", command);
            Runtime.getRuntime().exec(s);
            Thread.sleep(2000);
        }
    }

    public synchronized void updateNetwork1(String netName, String mode, String ip, String netmask, String gateway, String dns1, String dns2) {
        Map<String, String> hisConfig = readNetworkConfig(netName);
        List<String> newConfigList = getNewConfig(hisConfig, mode, ip, netmask, gateway, dns1, dns2);
        writeConfig(newConfigList, netName);
        restartNetwork();
    }

    /**
     * 根据网卡名称读取配置
     *
     * @param netName
     * @return
     */
    private Map<String, String> readNetworkConfig(String netName) {
        Map<String, String> configMap = new HashMap<>();

        String path = getConfigPath(netName);
        logger.info("网卡配置，-111-配置路径--{}", path);
        File file = new File(path);
        if (!file.exists()) {
            logger.info("网卡配置，--配置不存在--{}", netName);
            return configMap;
        }

        FileReader fileReader = null;
        BufferedReader bufferedReader = null;
        try {
            fileReader = new FileReader(file);
            bufferedReader = new BufferedReader(fileReader);
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                if (StringUtils.isEmpty(line) || !line.contains("=")) {
                    continue;
                }
                String[] arrTemp = line.split("=");
                if (arrTemp.length != 2) {
                    continue;
                }
                configMap.put(arrTemp[0].toUpperCase(), arrTemp[1].replaceAll("\"", ""));
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fileReader != null) {
                try {
                    fileReader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        logger.info("网卡配置，-222-配置信息--{}", JSONObject.toJSONString(configMap));
        return configMap;
    }

    private NetworkConfig getNetworkConfig(String netName) {
        NetworkConfig networkConfig = new NetworkConfig();

        Map<String, String> configMap = readNetworkConfig(netName);

        networkConfig.setMode(configMap.get("BOOTPROTO").toUpperCase());
        // 模式是none的转为static
        if ("NONE".equals(networkConfig.getMode())) {
            networkConfig.setMode("STATIC");
        }
        networkConfig.setIp(configMap.get("IPADDR"));
        networkConfig.setNetmask(configMap.get("NETMASK"));
        // 配置中没有netmask，则获取prefix
        if (StringUtils.isEmpty(networkConfig.getNetmask())) {
            networkConfig.setNetmask(IpUtils.mask2ipMask(configMap.get("PREFIX")));
        }
        networkConfig.setGateway(configMap.get("GATEWAY"));
        networkConfig.setDns1(configMap.get("DNS1"));
        networkConfig.setDns2(configMap.get("DNS2"));

        // 动态ip需要另外获取ip和子网掩码
        if ("DHCP".equals(networkConfig.getMode())) {
            getIpAddr(netName, networkConfig);
            getGateway(networkConfig);
        }

        if ("STATIC".equals(networkConfig.getMode())) {
            getVisit(netName, networkConfig);
        }

        return networkConfig;
    }

    /**
     * 获取ip和掩码
     *
     * @param netName
     * @param networkConfig
     */
    public void getIpAddr(String netName, NetworkConfig networkConfig) {
        BufferedReader inetReader = null;
        Reader reader = null;
        InputStream inputStream = null;
        List<String> cmdOutput = new ArrayList();
        String line;
        try {
            Process process = Runtime.getRuntime().exec("ip addr");
            inputStream = process.getInputStream();
            reader = new InputStreamReader(inputStream);
            inetReader = new BufferedReader(reader);

            while ((line = inetReader.readLine()) != null) {
                cmdOutput.add(line);
            }
            logger.info("网卡配置，ip addr--0000，--- {}", JSONObject.toJSONString(cmdOutput));

            for (String str : cmdOutput) {
                logger.info("网卡配置，ip addr--1111，--- {}", str);
                if (str.contains("inet") && str.contains("brd") && str.contains(netName)) {
                    String[] arr = str.trim().split(" ");
                    logger.info("网卡配置，ip addr--222，--- {}", JSONObject.toJSONString(arr));
                    if (arr.length < 2) {
                        continue;
                    }
                    String[] arr1 = arr[1].split("/");
                    logger.info("网卡配置，ip addr--333，--- {}", JSONObject.toJSONString(arr1));
                    if (arr1.length < 2) {
                        continue;
                    }
                    networkConfig.setIp(arr1[0]);
                    networkConfig.setNetmask(IpUtils.mask2ipMask(arr1[1]));
                }
            }

            process.waitFor();
        } catch (Exception e) {
            logger.error("get inetnames error--{}", e);
            return;
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
                if (null != reader) {
                    reader.close();
                }
                if (null != inetReader) {
                    inetReader.close();
                }
            } catch (IOException e) {
                logger.error("close inputstream error.");
            }
        }
    }

    /**
     * 获取网关
     *
     * @param networkConfig
     */
    public void getGateway(NetworkConfig networkConfig) {
        BufferedReader inetReader = null;
        Reader reader = null;
        InputStream inputStream = null;
        List<String> cmdOutput = new ArrayList();
        String line;
        try {
            Process process = Runtime.getRuntime().exec("ip route");
            inputStream = process.getInputStream();
            reader = new InputStreamReader(inputStream);
            inetReader = new BufferedReader(reader);

            while ((line = inetReader.readLine()) != null) {
                cmdOutput.add(line);
            }
            logger.info("网卡配置，ip route--0000，--- {}", JSONObject.toJSONString(cmdOutput));

            for (String str : cmdOutput) {
                logger.info("网卡配置，ip route--1111，--- {}", str);
                if (str.contains("default via")) {
                    String[] arr = str.trim().split(" ");
                    logger.info("网卡配置，ip addr--222，--- {}", JSONObject.toJSONString(arr));
                    networkConfig.setGateway(arr[2]);
                }
            }
            process.waitFor();
        } catch (Exception e) {
            logger.error("get inetnames error--{}", e);
            return;
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
                if (null != reader) {
                    reader.close();
                }
                if (null != inetReader) {
                    inetReader.close();
                }
            } catch (IOException e) {
                logger.error("close inputstream error.");
            }
        }
    }

    /**
     * 获取访问网段
     *
     * @param networkConfig
     */
    public void getVisit(String netName, NetworkConfig networkConfig) {
        BufferedReader inetReader = null;
        Reader reader = null;
        InputStream inputStream = null;
        List<String> cmdOutput = new ArrayList();
        String line;
        try {
            Process process = Runtime.getRuntime().exec("ip route");
            inputStream = process.getInputStream();
            reader = new InputStreamReader(inputStream);
            inetReader = new BufferedReader(reader);

            while ((line = inetReader.readLine()) != null) {
                cmdOutput.add(line);
            }
            logger.info("网卡配置，ip route--0000，--- {}", JSONObject.toJSONString(cmdOutput));

            // 访问网段列表
            List<String> visitList = new ArrayList<>();

            for (String str : cmdOutput) {
                logger.info("网卡配置，ip route--5555，--- {}", str);
                if (str.contains("via") && !str.contains("default via") && str.contains(netName)) {
                    visitList.add(str);
                }
            }
            // 访问网段
            if (visitList.size() > 0) {
                if (StringUtils.isNotEmpty(visitList.get(0))) {
                    String[] arr = visitList.get(0).trim().split(" ");
                    networkConfig.setVisit1(arr[0]);
                    networkConfig.setGateway(arr[2]);
                }
                if (StringUtils.isNotEmpty(visitList.get(1))) {
                    String[] arr = visitList.get(1).trim().split(" ");
                    networkConfig.setVisit2(arr[0]);
                    networkConfig.setGateway(arr[2]);
                }
                if (StringUtils.isNotEmpty(visitList.get(2))) {
                    String[] arr = visitList.get(2).trim().split(" ");
                    networkConfig.setVisit3(arr[0]);
                    networkConfig.setGateway(arr[2]);
                }
            }

            process.waitFor();
        } catch (Exception e) {
            logger.error("get inetnames error--{}", e);
            return;
        } finally {
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
                if (null != reader) {
                    reader.close();
                }
                if (null != inetReader) {
                    inetReader.close();
                }
            } catch (IOException e) {
                logger.error("close inputstream error.");
            }
        }
    }

    /**
     * 获取网卡配置文件路径
     *
     * @param ethName
     * @return
     */
    private String getConfigPath(String ethName) {
        return "/etc/sysconfig/network-scripts/ifcfg-" + ethName;
    }

    /**
     * 获取更新后的网卡配置
     *
     * @param ip
     * @param netmask
     * @param gateway
     * @return
     */
    public List<String> getNewConfig(Map<String, String> hisConfig, String mode, String ip, String netmask, String gateway, String dns1, String dns2) {
        List<String> newConfigList = new ArrayList<>();

        newConfigList.add("TYPE=Ethernet");
        newConfigList.add("PROXY_METHOD=none");
        newConfigList.add("BROWSER_ONLY=no");

        newConfigList.add("BOOTPROTO=" + mode);
        if ("STATIC".equals(mode)) {
            newConfigList.add("IPADDR=" + ip);
            newConfigList.add("NETMASK=" + netmask);
            if (!StringUtils.isEmpty(gateway)) {
                newConfigList.add("GATEWAY=" + gateway);
            }
        }
        if (!StringUtils.isEmpty(dns1)) {
            newConfigList.add("DNS1=" + dns1);
        }
        if (!StringUtils.isEmpty(dns2)) {
            newConfigList.add("DNS2=" + dns2);
        }
        newConfigList.add("DEFROUTE=yes");
        newConfigList.add("IPV4_FAILURE_FATAL=no");
        newConfigList.add("IPV6INIT=yes");
        newConfigList.add("IPV6_AUTOCONF=yes");
        newConfigList.add("IPV6_DEFROUTE=yes");
        newConfigList.add("IPV6_FAILURE_FATAL=no");
        newConfigList.add("IPV6_ADDR_GEN_MODE=stable-privacy");
        newConfigList.add("NAME=" + hisConfig.get("NAME"));
        newConfigList.add("UUID=" + hisConfig.get("UUID"));
        newConfigList.add("DEVICE=" + hisConfig.get("DEVICE"));
        newConfigList.add("ONBOOT=yes");

        logger.info("网卡配置，-333-更新后--{}", JSONObject.toJSONString(newConfigList));
        return newConfigList;
    }

    /**
     * 写配置
     *
     * @param configList
     * @param netName
     */
    public void writeConfig(List<String> configList, String netName) {
        String path = getConfigPath(netName);
        File file = new File(path);
        if (!file.exists()) {
            return;
        }

        FileWriter fileWriter = null;
        BufferedWriter bufferedWriter = null;
        try {
            fileWriter = new FileWriter(file);
            bufferedWriter = new BufferedWriter(fileWriter);
            for (String line : configList) {
                bufferedWriter.write(line);
                bufferedWriter.newLine();
            }
            logger.info("网卡配置，-444-更新成功");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bufferedWriter != null) {
                try {
                    bufferedWriter.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fileWriter != null) {
                try {
                    fileWriter.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 重启服务
     */
    public void restartNetwork() {
        String command = "service network restart";
        try {
            Runtime.getRuntime().exec(command);
        } catch (Exception e) {
            logger.error("service network restart error.");
        }
    }

    /**
     * 重启服务
     */
    public String getNetworkStatus() {
        StringBuilder sb = new StringBuilder();

        String command = "systemctl status network";
        try {
            Process process = Runtime.getRuntime().exec(command);

            InputStream execOut = process.getInputStream();
            InputStreamReader execReader = new InputStreamReader(execOut);
            char[] buffer = new char[1024];
            int bytesRead = execReader.read(buffer);
            while (bytesRead > 0) {
                sb.append(buffer, 0, bytesRead);
                bytesRead = execReader.read(buffer);
            }
        } catch (Exception e) {
            logger.error("systemctl status network error.");
        }
        return sb.toString();
    }

    /**
     * ip合法性验证
     *
     * @param ip
     * @return
     */
    public boolean ipCheck(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        // 定义正则表达式
        String regex = "^(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|[1-9])\\." +
                "(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\." +
                "(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\." +
                "(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$";
        // 判断ip地址是否与正则表达式匹配
        return ip.matches(regex);
    }
}
