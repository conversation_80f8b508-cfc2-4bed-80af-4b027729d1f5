package com.xhjt.project.feign;

import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * class
 *
 * <AUTHOR>
 */
@FeignClient(name = "dola", url = "${feign.dolaUrl}")
public interface DolaApi {

    /**
     * 原始数据传输过去进行解析
     *
     * @param jsonObject
     * @return
     */
    @GetMapping("/api/data/parseMsgList")
    String parseMsgList(@RequestParam(value = "jsonObject") String jsonObject);
}
