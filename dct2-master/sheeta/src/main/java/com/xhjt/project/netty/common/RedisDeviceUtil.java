package com.xhjt.project.netty.common;

import com.xhjt.common.RedisParameter;
import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.framework.redis.RedisCache;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.system.domain.SysNotice;
import com.xhjt.project.system.service.ISysNoticeService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * class
 *
 * <AUTHOR>
 */
public class RedisDeviceUtil {

    protected static Logger logger = LoggerFactory.getLogger(RedisDeviceUtil.class);

    /**
     * 设备
     */
    private static RedisCache redisCache = SpringUtils.getBean(RedisCache.class);


    public static void addAllDevice() {
        DeviceService deviceService = SpringUtils.getBean(DeviceService.class);
        List<DeviceEntity> list = deviceService.selectDeviceList(null);
        for (DeviceEntity device : list) {
            redisCache.setCacheObject("DEVICE"+device.getCode(), device);
        }
        //更新sn号到redis 方便pazu 传输时使用
        ISysNoticeService sysNoticeService = SpringUtils.getBean(ISysNoticeService.class);
        SysNotice sysOld = sysNoticeService.selectNoticeByContentAndRemark(null, RedisParameter.SHIP_SN);
        if (sysOld!=null){
            logger.info("sn号:---{}",sysOld.getNoticeContent().trim());
            redisCache.setCacheObject(RedisParameter.SHIP_SN, sysOld.getNoticeContent().trim());
        }
    }

    public static DeviceEntity getDevice(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        return redisCache.getCacheObject("DEVICE"+code);
    }

    public static void renewDevice(DeviceEntity device) {
        redisCache.setCacheObject("DEVICE"+device.getCode(), device);
    }

    public static void removeDevice(String code) {
        redisCache.deleteObject("DEVICE"+code);
    }
}
