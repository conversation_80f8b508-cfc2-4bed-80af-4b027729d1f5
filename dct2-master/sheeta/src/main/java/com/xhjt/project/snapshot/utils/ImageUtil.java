package com.xhjt.project.snapshot.utils;

import com.xhjt.common.utils.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.File;
import java.io.IOException;

/**
 * class
 *
 * <AUTHOR>
 */
public class ImageUtil {

    protected static Logger logger = LoggerFactory.getLogger(ImageUtil.class);

    public static boolean appraisalSnapshot(String imagePath) {
        boolean result = false;
        try {
            logger.info("鉴定图片--开始---{}-", imagePath);
            BufferedImage bf = readImage(imagePath);
            int[][] rgbArray = convertImageToArray(bf);
            result = checkTrueImage(rgbArray);
            logger.info("鉴定图片--结果----{}", result);

            if (!result) {
                logger.info("鉴定图片--删除异常图片----");
                FileUtils.deleteFile(imagePath);
            }
        } catch (Exception e) {
            System.out.print(e.getMessage());
        }
        return result;
    }

    private static BufferedImage readImage(String imageFile) {
        File file = new File(imageFile);
        BufferedImage bf = null;
        try {
            bf = ImageIO.read(file);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return bf;
    }

    private static int[][] convertImageToArray(BufferedImage bf) {
        // 获取图片宽度和高度
        int width = bf.getWidth();
        int height = bf.getHeight();
        // 将图片sRGB数据写入一维数组
        int[] data = new int[width * height];
        bf.getRGB(0, 0, width, height, data, 0, width);
        // 将一维数组转换为为二维数组
        int[][] rgbArray = new int[width][height / 2];
        for (int i = 0; i < height / 2; i++) {
            for (int j = 0; j < width; j++) {
                rgbArray[j][i] = data[(i + height / 2) * width + j];
            }
        }
        return rgbArray;
    }

    private static boolean checkTrueImage(int[][] rgbArray) {
        int width = rgbArray[0].length;
        int height = rgbArray.length;
        int totalNum = 0;
        int fcNum = 0;
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j += 5) {
                double fc = getVariance(rgbArray[i][j], rgbArray[i][j + 1], rgbArray[i][j + 2], rgbArray[i][j + 3], rgbArray[i][j + 4]);
                if (fc < 1) {
                    fcNum++;
                }
                totalNum++;
            }
        }

        return (fcNum * 1.0) / totalNum < 0.2;
    }

    private static double getVariance(int a, int b, int c, int d, int e) {
        double av = (a + b + c + d + e) / 5;
        double f = (a - av) * (a - av) + (b - av) * (b - av) + (c - av) * (c - av) + (d - av) * (d - av) + (e - av) * (e - av);
        return f / 5;
    }

    private static void writeImageFromArray(String imageFile, String type, int[][] rgbArray) {
        // 获取数组宽度和高度
        int width = rgbArray[0].length;
        int height = rgbArray.length;
        // 将二维数组转换为一维数组
        int[] data = new int[width * height];
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j++) {
                data[i * width + j] = rgbArray[i][j];
            }
        }
        // 将数据写入BufferedImage
        BufferedImage bf = new BufferedImage(width, height, BufferedImage.TYPE_INT_BGR);
        bf.setRGB(0, 0, width, height, data, 0, width);
        // 输出图片
        try {
            File file = new File(imageFile);
            ImageIO.write((RenderedImage) bf, type, file);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        try {
            Long t = System.currentTimeMillis();
            appraisalSnapshot("C:\\Users\\<USER>\\Desktop\\linshi\\tuowei\\1632063600000.jpg");
            System.out.println(System.currentTimeMillis() - t);
        } catch (Exception e) {
            System.out.print(e.getMessage());
        }
    }
}
