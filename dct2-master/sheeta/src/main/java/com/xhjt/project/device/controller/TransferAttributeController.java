package com.xhjt.project.device.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.redis.RedisCache;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.domain.DefaultAttribute;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.TransferDeviceVo;
import com.xhjt.project.device.service.DefaultAttributeService;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.TransferAttributeService;
import com.xhjt.project.monitor.domain.SysOperLog;
import com.xhjt.project.monitor.service.ISysOperLogService;
import com.xhjt.project.netty.common.DeviceUtil;
import com.xhjt.project.netty.common.RedisDeviceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/transferAttribute")
public class TransferAttributeController extends BaseController {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private TransferAttributeService transferAttributeService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysOperLogService iSysOperLogService;
    @Autowired
    private DefaultAttributeService defaultAttributeService;
    /**
     * 获取传输属性列表
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceEntity deviceEntity) {
        startPage();
        List<DeviceEntity> deviceList = deviceService.selectDeviceList(deviceEntity);
        List<TransferAttributeEntity> taList = transferAttributeService.selectTransferAttributeList(new TransferAttributeEntity());
        Map<String, String> map = taList.stream()
                .collect(groupingBy(TransferAttributeEntity::getDeviceCode,
                        mapping(TransferAttributeEntity::getLabel, Collectors.joining(","))));

        deviceList.forEach(device -> {
            //从redis中取出来，加入最新数据保存到hbase的时间
            if (redisCache.getCacheObject(device.getCode()) == null){
                //一开始可能还没有则给个当前的时间
                String dt = DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
                device.setLastTime(dt);
            }else{
                long lastTime = redisCache.getCacheObject(device.getCode());
                String dt = DateUtils.parseTimeToDate(lastTime,"yyyy-MM-dd HH:mm:ss");
                device.setLastTime(dt);
            }

            if (map.get(device.getCode()) != null) {
                device.setAttributes(map.get(device.getCode()));
            }
        });

        return getDataTable(deviceList);
    }

    @Log(title = "传输属性", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('transfer:attribute:export')")
    @GetMapping("/export")
    public AjaxResult export(TransferAttributeEntity transferAttribute) {
        List<TransferAttributeEntity> list = transferAttributeService.selectTransferAttributeList(transferAttribute);
        ExcelUtil<TransferAttributeEntity> util = new ExcelUtil<TransferAttributeEntity>(TransferAttributeEntity.class);
        return util.exportExcel(list, "传输数据");
    }

    @PreAuthorize("@ss.hasPermi('transfer:attribute:list')")
    @GetMapping("/listByDeviceCode")
    public AjaxResult listByDeviceCode(TransferAttributeEntity transferAttribute) {
        List<TransferAttributeEntity> list = transferAttributeService.selectTransferAttributeList(transferAttribute);
        return AjaxResult.success(list);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:query')")
    @GetMapping(value = "/{transferAttributeId}")
    public AjaxResult getInfo(@PathVariable Long transferAttributeId) {
        return AjaxResult.success(transferAttributeService.selectTransferAttributeById(transferAttributeId));
    }

    /**
     * 新增传输属性
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:add')")
    @Log(title = "传输属性新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody TransferAttributeEntity transferAttribute) {
        transferAttribute.setCreateBy(SecurityUtils.getUsername());
        return toAjax(transferAttributeService.addTransferAttribute(transferAttribute));
    }

    /**
     * 修改传输属性
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:edit')")
    @Log(title = "传输属性修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody TransferAttributeEntity transferAttribute) {
        transferAttribute.setUpdateBy(SecurityUtils.getUsername());
        List<TransferAttributeEntity> transferAttributeEntities =  transferAttributeService.updateTransferAttribute(transferAttribute);

        if (transferAttributeEntities!=null && transferAttributeEntities.size() > 0) {
            //这边需要把cost , compartment更新到设备信息表DeviceEntity
            DeviceEntity deviceEntity = deviceService.selectByCode(transferAttribute.getDeviceCode());
            deviceEntity.setCost(transferAttribute.getCost());
            deviceEntity.setCompartment(transferAttribute.getCompartment());
            int row = deviceService.updateDevice(deviceEntity);
            if (row>0){
                TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
                transferDeviceEntity.setDeviceEntity(deviceEntity);
                transferDeviceEntity.setTransferAttributeEntities(transferAttributeEntities);
                deviceService.syncNewShore(transferDeviceEntity, 2);
                transferAttributeService.renewAttributes(transferAttribute.getDeviceCode());

                //同步更新设备缓存数据
                RedisDeviceUtil.renewDevice(deviceEntity);
                DeviceUtil.renewDevice(deviceEntity);
            }

        }


        return toAjax(transferAttributeEntities.size());
    }

    /**
     * 定时任务-存储状态修改
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:edit')")
    @PostMapping("/changeStatus/{code}/{status}")
    public AjaxResult changeStatus(@PathVariable String code, @PathVariable Integer status) {
        DeviceEntity deviceEntity = deviceService.selectByCode(code);
        if (status==0){
            DeviceEntity device = deviceService.disable(deviceEntity.getId());
            if (device!=null){
                List<TransferAttributeEntity> transferAttributeEntities = transferAttributeService.selectListByCode(deviceEntity.getCode());
                TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
                transferDeviceEntity.setDeviceEntity(device);
                transferDeviceEntity.setTransferAttributeEntities(transferAttributeEntities);
                deviceService.syncNewShore(transferDeviceEntity, 2);
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("设备传输'"+device.getName()+"'(存储状态)关闭");
                iSysOperLogService.insertOperlog(sysOperLog);
                return toAjax(1);
            }else {
                return toAjax(0);
            }
        }
        DeviceEntity device = deviceService.enable(deviceEntity.getId());
        if (device!=null){
            List<TransferAttributeEntity> transferAttributeEntities = transferAttributeService.selectListByCode(deviceEntity.getCode());
            TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
            transferDeviceEntity.setDeviceEntity(device);
            transferDeviceEntity.setTransferAttributeEntities(transferAttributeEntities);
            deviceService.syncNewShore(transferDeviceEntity, 2);
            logger.error(status.toString());
            if(status != null && status == 1){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("设备传输'"+device.getName()+"'(存储状态)启动");
                iSysOperLogService.insertOperlog(sysOperLog);
            }
            return toAjax(1);
        }else {
            return toAjax(0);
        }
    }

    /**
     * 定时任务-传输状态修改
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:edit')")
    @PostMapping("/changeTrStatus/{code}/{status}")
    public AjaxResult changeTrStatus(@PathVariable String code, @PathVariable Integer status) {
        DeviceEntity deviceEntity = deviceService.selectByCode(code);
        DeviceEntity device = deviceService.changeTrStatus(deviceEntity.getId(),status);
        if (device!=null){
            List<TransferAttributeEntity> transferAttributeEntities = transferAttributeService.selectListByCode(deviceEntity.getCode());
            TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
            transferDeviceEntity.setDeviceEntity(device);
            transferDeviceEntity.setTransferAttributeEntities(transferAttributeEntities);
            deviceService.syncNewShore(transferDeviceEntity, 2);
            if(status != null && status == 1){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("设备传输'"+device.getName()+"'(传输状态)启动");
                iSysOperLogService.insertOperlog(sysOperLog);
            }else if(status != null && status == 0){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("设备传输'"+device.getName()+"'(传输状态)关闭");
                iSysOperLogService.insertOperlog(sysOperLog);
            }
            return toAjax(1);
        }else {
            return toAjax(0);
        }
    }

    /**
     * 获取设备默认属性集合
     * @param defaultAttribute
     * @return
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:list')")
    @GetMapping("/listDefaultsByType")
    public AjaxResult listDefaultsByType(DefaultAttribute defaultAttribute) {
         List<DefaultAttribute> list = defaultAttributeService.selectDefaultAttributeList(defaultAttribute);
        return AjaxResult.success(list);
    }
}
