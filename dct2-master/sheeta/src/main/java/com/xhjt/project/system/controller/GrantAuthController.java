package com.xhjt.project.system.controller;

import com.xhjt.common.RedisParameter;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.utils.EncrypDESUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.redis.RedisCache;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.system.domain.SysNotice;
import com.xhjt.project.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * 授权管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/grant/auth")
public class GrantAuthController extends BaseController {

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysNoticeService sysNoticeService;

    @PreAuthorize("@ss.hasPermi('system:grant:list')")
    @Log(title = "授权配置", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importSnAuth(@RequestParam("type") Integer type, @RequestParam("file") MultipartFile file) {
        //目前sn已完成注入
        String result = readFile(file);
        if (StringUtils.isBlank(result)) {
            return AjaxResult.error("授权配置文件异常");
        }

        if (type == 1) {
            return snGrant(result);
        }
        // TODO:配置License-待需求确认
        if (type == 2) {

        }

        return AjaxResult.success("授权配置成功");
    }

    /**
     * sn配置
     * @param secretStr
     * @return
     */
    private AjaxResult snGrant(String secretStr) {
        String sourceStr = EncrypDESUtils.strDecrypt(secretStr.trim());
        if (StringUtils.isNotBlank(sourceStr)) {
            if (!sourceStr.contains("sn=")) {
                return AjaxResult.error("sn配置文件无效");
            }
            String[] arr = sourceStr.split("=");
            if (arr.length != 2) {
                return AjaxResult.error("sn配置文件内容无效");
            }
            //更新放到redis
            redisCache.setCacheObject(RedisParameter.SHIP_SN, arr[1]);
            //暂时入库到公告
            SysNotice sysOld = sysNoticeService.selectNoticeByContentAndRemark(arr[1], RedisParameter.SHIP_SN);
            if (sysOld != null) {
                sysOld.setNoticeContent(arr[1]);
                sysOld.setUpdateBy(SecurityUtils.getUsername());
                sysNoticeService.updateNotice(sysOld);
            } else {
                SysNotice sysNotice = new SysNotice();
                sysNotice.setNoticeTitle("岸端sn号");
                sysNotice.setNoticeType("2");
                sysNotice.setNoticeContent(arr[1]);
                sysNotice.setStatus("1");
                sysNotice.setRemark(RedisParameter.SHIP_SN);
                sysNotice.setCreateBy(SecurityUtils.getUsername());
                sysNoticeService.insertNotice(sysNotice);
            }
        } else {
            return AjaxResult.error("解密错误");
        }

        return AjaxResult.success("sn配置成功");
    }

    /**
     * 读取文本内容
     *
     * @param file
     * @return
     */
    private String readFile(MultipartFile file) {
        String result = "";
        try {
            InputStream is = file.getInputStream();
            byte[] bytes = new byte[100];
            int i;
            int index = 0;
            while (((i = is.read()) != -1)) {
                bytes[index] = (byte) i;
                index++;
            }
            result = new String(bytes);
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    public static void main(String[] args){
        String ss="dede06e232dc7d30f1581184d94ed4b7";
        String sourceStr = EncrypDESUtils.strDecrypt(ss);
        if (sourceStr.contains("sn=")) {
            System.out.println(sourceStr+sourceStr.startsWith("sn="));
            String[] arr = sourceStr.split("=");
            System.out.println(arr.length);
            if (arr.length != 2) {
                System.out.println(arr);
            }
        }

    }
}
