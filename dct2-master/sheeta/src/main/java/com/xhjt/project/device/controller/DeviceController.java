package com.xhjt.project.device.controller;

import com.xhjt.common.SerialPortUtil;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.redis.RedisCache;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.domain.*;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.SerialConfigService;
import com.xhjt.project.device.service.TransferAttributeService;
import com.xhjt.project.monitor.domain.SysOperLog;
import com.xhjt.project.monitor.service.ISysOperLogService;
import com.xhjt.project.netty.common.AmUtil;
import com.xhjt.project.netty.common.DeviceUtil;
import com.xhjt.project.netty.common.RedisDeviceUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 设备信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/device")
public class DeviceController extends BaseController {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private TransferAttributeService transferAttributeService;
    @Autowired
    private ISysOperLogService iSysOperLogService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SerialConfigService serialConfigService;


    /**
     * 获取设备信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceEntity device) {
        startPage();
        List<DeviceEntity> list = deviceService.selectDeviceList(device);
        if (list!=null && list.size()>0){
            list.stream().forEach(deviceEntity -> {
                if(deviceEntity.getConnectType() == 1){
                     String data = redisCache.getCacheObject("CJ_CK_"+deviceEntity.getSerialPort());
                    if (StringUtils.isNotBlank(data)){
                        //有数据则说明5分钟内是正常连接得
                        deviceEntity.setConnectStatus(1);
                    }else {
                        deviceEntity.setConnectStatus(0);
                    }

                }
            });
        }
        return getDataTable(list);
    }

    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('device:info:export')")
    @GetMapping("/export")
    public AjaxResult export(DeviceEntity device) {
        List<DeviceEntity> list = deviceService.selectDeviceList(device);
        ExcelUtil<DeviceEntity> util = new ExcelUtil<DeviceEntity>(DeviceEntity.class);
        return util.exportExcel(list, "设备数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        ModifyDeviceEntity modifyDeviceEntity = new ModifyDeviceEntity();
        DeviceEntity deviceEntity = deviceService.selectDeviceById(id);
        BeanUtils.copyProperties(deviceEntity,modifyDeviceEntity);
        return AjaxResult.success(modifyDeviceEntity);
    }

    /**
     * 新增设备信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:add')")
    @Log(title = "设备新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ModifyDeviceEntity device) throws Exception {
        DeviceEntity deviceEntity1 = new DeviceEntity();
        BeanUtils.copyProperties(device,deviceEntity1);
        device.setCreateBy(SecurityUtils.getUsername());
        DeviceEntity deviceEntity = deviceService.addDevice(deviceEntity1);

        if (deviceEntity!=null) {
            //插入设备传输属性表
            TransferAttributeEntity transferAttribute = new TransferAttributeEntity();
            transferAttribute.setTransferAttributes(device.getTransferAttributes());
            transferAttribute.setDeviceCode(deviceEntity.getCode());
            transferAttribute.setDeviceName(deviceEntity.getName());
            transferAttribute.setUpdateBy(SecurityUtils.getUsername());
            List<TransferAttributeEntity> transferAttributeEntities = transferAttributeService.updateTransferAttribute(transferAttribute);

            if (transferAttributeEntities.size() > 0) {
                TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
                transferDeviceEntity.setDeviceEntity(deviceEntity);
                transferDeviceEntity.setTransferAttributeEntities(transferAttributeEntities);

                deviceService.syncNewShore(transferDeviceEntity, 1);
                transferAttributeService.renewAttributes(transferAttribute.getDeviceCode());
            }

            return toAjax(1);

        }

        return toAjax(0);
    }

    /**
     * 修改设备信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:edit')")
    @Log(title = "设备修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ModifyDeviceEntity device) {
        DeviceEntity deviceEntity = null;
        if (device.getConnectType()==1){
            deviceEntity = DeviceUtil.getDevice(device.getSerialPort()+device.getCode());
        }else {
            deviceEntity = DeviceUtil.getDevice(device.getMac()+device.getCode());
        }

        if (deviceEntity != null && !deviceEntity.getCode().equals(device.getCode())) {
            return AjaxResult.error("设备'" + device.getName() + "'修改失败，采集终端已被其他设备绑定");
        }
        device.setUpdateBy(SecurityUtils.getUsername());
        DeviceEntity deviceEntity1 = new DeviceEntity();
        BeanUtils.copyProperties(device,deviceEntity1);
        int rows = deviceService.updateDevice(deviceEntity1);

        if (rows > 0) {
            DeviceUtil.addAllDevice();
            RedisDeviceUtil.addAllDevice();
            //插入设备传输属性表
            TransferAttributeEntity transferAttribute = new TransferAttributeEntity();
            transferAttribute.setTransferAttributes(device.getTransferAttributes());
            transferAttribute.setDeviceCode(deviceEntity1.getCode());
            transferAttribute.setDeviceName(deviceEntity1.getName());
            transferAttribute.setUpdateBy(SecurityUtils.getUsername());
            List<TransferAttributeEntity> transferAttributeEntities = transferAttributeService.updateTransferAttribute(transferAttribute);
            if (transferAttributeEntities.size() > 0) {
                TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
                transferDeviceEntity.setDeviceEntity(deviceEntity1);
                transferDeviceEntity.setTransferAttributeEntities(transferAttributeEntities);
                deviceService.syncNewShore(transferDeviceEntity, 2);
                transferAttributeService.renewAttributes(transferAttribute.getDeviceCode());
            }
        }
        return toAjax(rows);
    }

    /**
     * 删除设备信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:remove')")
    @Log(title = "设备删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        List<DeviceEntity> list = Arrays.stream(ids).map(id -> deviceService.selectDeviceById(id)).collect(Collectors.toList());
        int rows = deviceService.deleteDeviceByIds(ids);

        if (rows > 0) {
            list.stream().forEach(device -> {
                TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
                transferDeviceEntity.setDeviceEntity(device);
                transferDeviceEntity.setTransferAttributeEntities(null);
                deviceService.syncNewShore(transferDeviceEntity, 3);
            });
            DeviceUtil.addAllDevice();
            RedisDeviceUtil.addAllDevice();
        }
        SerialPortUtil.findSystemAllSerialPort();
        return toAjax(rows);
    }

    /**
     * 启用
     */
    @PreAuthorize("@ss.hasPermi('device:info:enable')")
    @PostMapping(value = "/enable/{id}")
    public AjaxResult enable(@PathVariable Long id) {
        DeviceEntity deviceEntity = deviceService.selectDeviceById(id);
        List<TransferAttributeEntity> attributeEntities = transferAttributeService.selectListByCode(deviceEntity.getCode());
        if (attributeEntities.size() == 0) {
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("设备'" + deviceEntity.getName() + "'(存储状态)启用失败");
            iSysOperLogService.insertOperlog(sysOperLog);
            return AjaxResult.error("设备'" + deviceEntity.getName() + "'启用失败，未配置传输属性");
        }

        DeviceEntity device = deviceService.enable(id);
        if (device!=null){
            TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
            transferDeviceEntity.setDeviceEntity(device);
            transferDeviceEntity.setTransferAttributeEntities(attributeEntities);
            deviceService.syncNewShore(transferDeviceEntity, 2);
        }
        return AjaxResult.success(device);
    }

    /**
     * 禁用
     */
    @PreAuthorize("@ss.hasPermi('device:info:disable')")
    @PostMapping(value = "/disable/{id}")
    public AjaxResult disable(@PathVariable Long id) {
        DeviceEntity device = deviceService.disable(id);
        if (device!=null) {
            List<TransferAttributeEntity> attributeEntities = transferAttributeService.selectListByCode(device.getCode());
            TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
            transferDeviceEntity.setDeviceEntity(device);
            transferDeviceEntity.setTransferAttributeEntities(attributeEntities);
            deviceService.syncNewShore(transferDeviceEntity, 2);
        }
        return AjaxResult.success(device);
    }

    /**
     * 定时任务-传输状态修改
     */
    @PreAuthorize("@ss.hasPermi('device:info:edit')")
    @PostMapping("/changeTrStatus/{id}/{status}")
    public AjaxResult changeTrStatus(@PathVariable Long id, @PathVariable Integer status) {
        DeviceEntity device = deviceService.changeTrStatus(id,status);
        if (device!=null){
            List<TransferAttributeEntity> attributeEntities = transferAttributeService.selectListByCode(device.getCode());
            TransferDeviceVo transferDeviceEntity = new TransferDeviceVo();
            transferDeviceEntity.setDeviceEntity(device);
            transferDeviceEntity.setTransferAttributeEntities(attributeEntities);
            deviceService.syncNewShore(transferDeviceEntity, 2);
            if(status != null && status == 1){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("设备'"+device.getName()+"'(传输状态)启动");
                iSysOperLogService.insertOperlog(sysOperLog);
            }else if(status != null && status == 0){
                SysOperLog sysOperLog = new SysOperLog();
                sysOperLog.setTitle("设备'"+device.getName()+"'(传输状态)关闭");
                iSysOperLogService.insertOperlog(sysOperLog);
            }
            return toAjax(1);
        }else {
            return toAjax(0);
        }
    }

    /**
     * 获取空闲串口
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/getFreeSerialPort")
    public AjaxResult getFreeSerialPort() {
        //直接获取串口配置表里面得数据
        List<SerialConfig> serialConfigs = serialConfigService.selectSerialConfigList(null);
        List<String> portNameList = new ArrayList<String>();
        if (serialConfigs!=null && serialConfigs.size()>0){
            portNameList = serialConfigs.stream().map(serialConfig -> serialConfig.getNewName()).collect(Collectors.toList());
        }else {
            return AjaxResult.success(new ArrayList<>());
        }
        if (portNameList == null) {
            return AjaxResult.success(new ArrayList<>());
        }

        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setConnectType(1);//串口
        List<DeviceEntity> list = deviceService.selectDeviceList(deviceEntity);
        for (DeviceEntity portInfo : list) {
            portNameList.remove(portInfo.getSerialPort());
        }
        return AjaxResult.success(portNameList);
    }

    /**
     * 获取全部串口
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/getAllSerialPort")
    public AjaxResult getAllSerialPort() {
        //直接获取串口配置表里面得数据
        List<SerialConfig> serialConfigs = serialConfigService.selectSerialConfigList(null);
        List<String> portNameList = new ArrayList<String>();
        if (serialConfigs!=null && serialConfigs.size()>0){
            portNameList = serialConfigs.stream().map(serialConfig -> serialConfig.getNewName()).collect(Collectors.toList());
        }

        return AjaxResult.success(portNameList);
    }

    /**
     * 串口新增预览-创建串口临时监听
     */
    @PreAuthorize("@ss.hasPermi('device:info:edit')")
    @PostMapping("/serialPortView")
    public AjaxResult serialPortView(@RequestBody ModifyDeviceEntity device) {
        DeviceEntity deviceEntity = new DeviceEntity();
        BeanUtils.copyProperties(device,deviceEntity);
//        deviceEntity.setSerialPort(serialPort);
//        deviceEntity.setBaudRate(baudRate);
//        deviceEntity.setDataBits(dataBits);
//        deviceEntity.setStopBits(stopBits);
//        deviceEntity.setParity(parity);
        int row = deviceService.serialPortView(deviceEntity);
        return toAjax(row);
    }

    /**
     * 串口新增预览-关闭串口临时监听
     */
    @PreAuthorize("@ss.hasPermi('device:info:edit')")
    @PostMapping("/delSerialPortView")
    public AjaxResult delSerialPortView(@RequestBody ModifyDeviceEntity device) {
         deviceService.delSerialPortView(device.getSerialPort());
        return toAjax(1);
    }
    /**
     * 修改时获取表格表头
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/getTableHead/{deviceId}")
    public AjaxResult queryBySn(@PathVariable Long deviceId) {
        return AjaxResult.success(deviceService.getTableHead(deviceId));
    }

    /**
     * 新增操作时获取表格表头
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/getTypeTableHead/{type}")
    public AjaxResult getTypeTableHead(@PathVariable Integer type) {
        return AjaxResult.success(deviceService.getTableHeadByType(type));
    }

    /**
     * 采集终端配置
     */
    @PreAuthorize("@ss.hasPermi('device:info:add')")
    @Log(title = "设备新增", businessType = BusinessType.INSERT)
    @PostMapping("/writeSerialPort")
    public AjaxResult writeSerialPort(@Validated @RequestBody WriteSerialPortEntity writeSerialPortEntity) throws Exception {

        //这边回写逻辑待定
        System.out.println(writeSerialPortEntity.toString());
        return toAjax(1);
    }

    /**
     * 获取原始串口集合
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/getOldFreeSerialPort")
    public AjaxResult getOldFreeSerialPort() {
        Set<String> portNameList = SerialPortUtil.getSerialPortNameSets();
        if (portNameList == null) {
            return AjaxResult.success(new ArrayList<>());
        }
        return AjaxResult.success(portNameList);
    }
}
