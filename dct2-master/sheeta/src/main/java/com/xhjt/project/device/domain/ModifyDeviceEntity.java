package com.xhjt.project.device.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;


/**
 * 设备-用于新增修改使用
 *
 * <AUTHOR>
 */
public class ModifyDeviceEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 绑定的采集中间件（当连接方式串口时 该值为串口号名称）
     */
    private String mac;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备编码
     */
    private String code;

    /**
     * 设备类型编码
     */
    private Integer type;

    /**
     * 优先级
     */
    private Integer cost;

    /**
     * 存储状态
     */
    private Integer enable;


    /**
     * 设备外网IP
     */
    private String ip;

    /**
     * 设备开放端口
     */
    private Integer port;

    /**
     * TCP连接状态
     */
    private Integer connectStatus;

    private String attributes;

    /**
     * 快照传输状态
     */
    private Integer transferStatus;
    /**
     * 传输间隔(单位：秒)
     */
    private Integer compartment;

    /**
     * 传输间隔--展示
     */
    private String compartmentStr;

    /**
     * 最新数据时间
     * @return
     */
    private String lastTime;

    /**
     * 连接方式
     * @return
     */
    private Integer connectType;

    /**
     * 串口号
     */
    private String serialPort;

    /**
     * 波特率
     */
    private Integer baudRate;

    /**
     * 数据位
     */
    private Integer dataBits;

    /**
     * 停止位
     */
    private Integer stopBits;

    /**
     * 校验位
     */
    private Integer parity;

    /**
     * 已选属性数组，前端传参使用
     */
    private String[] transferAttributes;

    public String[] getTransferAttributes() {
        return transferAttributes;
    }

    public void setTransferAttributes(String[] transferAttributes) {
        this.transferAttributes = transferAttributes;
    }

    public String getSerialPort() {
        return serialPort;
    }

    public void setSerialPort(String serialPort) {
        this.serialPort = serialPort;
    }

    public Integer getBaudRate() {
        return baudRate;
    }

    public void setBaudRate(Integer baudRate) {
        this.baudRate = baudRate;
    }

    public Integer getDataBits() {
        return dataBits;
    }

    public void setDataBits(Integer dataBits) {
        this.dataBits = dataBits;
    }

    public Integer getStopBits() {
        return stopBits;
    }

    public void setStopBits(Integer stopBits) {
        this.stopBits = stopBits;
    }

    public Integer getParity() {
        return parity;
    }

    public void setParity(Integer parity) {
        this.parity = parity;
    }

    public Integer getConnectType() {
        return connectType;
    }

    public void setConnectType(Integer connectType) {
        this.connectType = connectType;
    }

    public Integer getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(Integer transferStatus) {
        this.transferStatus = transferStatus;
    }

    public Integer getCompartment() {
        return compartment;
    }

    public void setCompartment(Integer compartment) {
        this.compartment = compartment;
    }

    public String getCompartmentStr() {
        if (this.compartment == null) {
            return "";
        }
        if (this.compartment >= 60) {
            return (this.compartment / 60) + "分钟";
        } else {
            return this.compartment + "秒钟";
        }
    }

    public void setCompartmentStr(String compartmentStr) {
        this.compartmentStr = compartmentStr;
    }

    public String getLastTime() {
        return lastTime;
    }

    public void setLastTime(String lastTime) {
        this.lastTime = lastTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getEnable() {
        return enable == null ? 0 : enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Integer getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(Integer connectStatus) {
        this.connectStatus = connectStatus;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }
}
