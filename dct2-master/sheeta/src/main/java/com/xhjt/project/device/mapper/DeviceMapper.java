package com.xhjt.project.device.mapper;


import com.xhjt.project.device.domain.DeviceEntity;

import java.util.List;

/**
 * 设备管理 数据层
 *
 * <AUTHOR>
 */
public interface DeviceMapper {
    /**
     * 查询设备信息
     *
     * @param device 设备信息
     * @return 设备信息
     */
    public DeviceEntity selectDevice(DeviceEntity device);




    /**
     * 查询设备列表
     *
     * @param device 设备信息
     * @return 设备集合
     */
    public List<DeviceEntity> selectDeviceList(DeviceEntity device);

    /**
     * 新增设备
     *
     * @param device 设备信息
     * @return 结果
     */
    public int addDevice(DeviceEntity device);

    /**
     * 修改设备
     *
     * @param device 设备信息
     * @return 结果
     */
    public int updateDevice(DeviceEntity device);

    /**
     * 删除设备
     *
     * @param deviceId 参数ID
     * @return 结果
     */
    public int deleteDeviceById(Long deviceId);

    /**
     * 批量删除参数信息
     *
     * @param deviceIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteDeviceByIds(Long[] deviceIds);


    /**
     * 从日志获取设备运行状态
     * @return
     */
    String getDeviceLog();
}
