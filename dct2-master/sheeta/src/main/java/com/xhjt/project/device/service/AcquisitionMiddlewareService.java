package com.xhjt.project.device.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.project.collect.domain.CollectModule;
import com.xhjt.project.collect.domain.CollectNode;
import com.xhjt.project.collect.service.CollectModuleService;
import com.xhjt.project.collect.service.CollectNodeService;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.SyncAllEntity;
import com.xhjt.project.device.mapper.AcquisitionMiddlewareMapper;
import com.xhjt.project.index.domain.ShipTerminalManage;
import com.xhjt.project.netty.common.DeviceUtil;
import com.xhjt.project.netty.service.StoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class AcquisitionMiddlewareService {
    protected static Logger logger = LoggerFactory.getLogger(AcquisitionMiddlewareService.class);

    @Autowired
    private AcquisitionMiddlewareMapper acquisitionMiddlewareMapper;

    @Autowired
    private ConnectLogService connectLogService;

    @Autowired
    private StoreService storeService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private CollectModuleService collectModuleService;
    @Autowired
    private CollectNodeService collectNodeService;
    /**
     * 查询设备信息
     *
     * @param id 设备ID
     * @return 设备信息
     */
    @Transactional(rollbackFor = Exception.class)
    public AcquisitionMiddleware selectAcquisitionMiddlewareById(Long id) {
        AcquisitionMiddleware acquisitionMiddleware = new AcquisitionMiddleware();
        acquisitionMiddleware.setId(id);
        return acquisitionMiddlewareMapper.selectAcquisitionMiddleware(acquisitionMiddleware);
    }

    /**
     * 查询设备信息
     *
     * @return 设备信息
     */
    public AcquisitionMiddleware selectAcquisitionMiddlewareByIp(String ip, Integer port) {
        AcquisitionMiddleware acquisitionMiddleware = new AcquisitionMiddleware();
        acquisitionMiddleware.setIp(ip);
        acquisitionMiddleware.setPort(port);

        try {
            AcquisitionMiddleware acquisitionMiddlewareOld = acquisitionMiddlewareMapper.selectAcquisitionMiddleware(acquisitionMiddleware);
            return acquisitionMiddlewareOld;
        } catch (Exception e) {
            deleteByIpAndPort(acquisitionMiddleware);
            delete4Invalid();
        }

        return null;
    }

    /**
     * 查询设备信息
     *
     * @return 设备信息
     */
    @Transactional(rollbackFor = Exception.class)
    public AcquisitionMiddleware selectAcquisitionMiddlewareByMac(String mac) {
        AcquisitionMiddleware acquisitionMiddleware = new AcquisitionMiddleware();
        acquisitionMiddleware.setMac(mac);
        return acquisitionMiddlewareMapper.selectAcquisitionMiddleware(acquisitionMiddleware);
    }

    public List<AcquisitionMiddleware> queryUnboundList() {
        List<AcquisitionMiddleware> queryList = acquisitionMiddlewareMapper.selectAcquisitionMiddlewareList(new AcquisitionMiddleware());
        List<AcquisitionMiddleware> list = Lists.newArrayList();

        for (AcquisitionMiddleware am : queryList) {
            if (StringUtils.isBlank(am.getMac()) || am.getConnectStatus() == 0) {
                continue;
            }
//            if (DeviceUtil.getDevice(am.getMac()) == null) {
            //去掉设备捆绑的限制，采集终端可对应多个设备
                list.add(am);
//            }
        }

        return list;
    }

    /**
     * 查询设备列表
     *
     * @param acquisitionMiddleware 设备信息
     * @return 设备集合
     */
    @Transactional(rollbackFor = Exception.class)
    public List<AcquisitionMiddleware> selectAcquisitionMiddlewareList(AcquisitionMiddleware acquisitionMiddleware) {
        return acquisitionMiddlewareMapper.selectAcquisitionMiddlewareList(acquisitionMiddleware);
    }

    /**
     * 新增设备
     *
     * @param acquisitionMiddleware 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addAcquisitionMiddleware(AcquisitionMiddleware acquisitionMiddleware) {
        return acquisitionMiddlewareMapper.addAcquisitionMiddleware(acquisitionMiddleware);
    }

    /**
     * 修改设备
     *
     * @param am 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateAcquisitionMiddleware(AcquisitionMiddleware am) {

        connectLogService.addConnectLog(am, 3);

        return acquisitionMiddlewareMapper.updateAcquisitionMiddleware(am);
    }

    /**
     * 删除设备信息
     *
     * @param id 参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteAcquisitionMiddlewareById(Long id) {
        return acquisitionMiddlewareMapper.deleteAcquisitionMiddlewareById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteAcquisitionMiddlewareByMac(String mac) {
        return acquisitionMiddlewareMapper.deleteAcquisitionMiddlewareByMac(mac);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByIpAndPort(AcquisitionMiddleware am) {
        return acquisitionMiddlewareMapper.deleteByIpAndPort(am);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete4Invalid() {
        return acquisitionMiddlewareMapper.delete4Invalid();
    }

    @Transactional(rollbackFor = Exception.class)
    public int connectStatusInvalid() {
        return acquisitionMiddlewareMapper.connectStatusInvalid();
    }
    /**
     * 批量删除参数信息
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteAcquisitionMiddlewareByIds(Long[] ids) {
        int row = acquisitionMiddlewareMapper.deleteAcquisitionMiddlewareByIds(ids);
        return row;
    }

    /**
     * 修改连接状态
     */
    @Transactional(rollbackFor = Exception.class)
    public AcquisitionMiddleware changeConnectStatus(String ip, Integer port, Integer status) {
        AcquisitionMiddleware am = selectAcquisitionMiddlewareByIp(ip, port);
        if (am == null) {
            am = new AcquisitionMiddleware();
            am.setIp(ip);
            am.setPort(port);
            am.setConnectStatus(status);
//            acquisitionMiddlewareMapper.addAcquisitionMiddleware(am);
        } else {
            am.setConnectStatus(status);
            acquisitionMiddlewareMapper.updateAcquisitionMiddleware(am);
        }

        int action = status == 1 ? 1 : 2;
        connectLogService.addConnectLog(am, action);

        return am;
    }


    /**
     * 采集终端同步到岸端
     * @param acquisitionMiddleware
     * @param action
     */
    public void syncNewShore(AcquisitionMiddleware acquisitionMiddleware, int action) {
        DeviceEntity device = new DeviceEntity();
        device.setMac(acquisitionMiddleware.getMac());
        device.setConnectType(0);//采集终端
        List<DeviceEntity> deviceEntity = deviceService.selectDeviceList(device);
//        DeviceEntity deviceEntity = deviceService.selectByMac(acquisitionMiddleware.getMac());
        List<CollectModule> collectModules = collectModuleService.selectCollectModuleByCode(acquisitionMiddleware.getCode());
        if (collectModules != null && collectModules.size() > 0) {
            acquisitionMiddleware.setName(collectModules.get(0).getName());
            acquisitionMiddleware.setModuleModel(collectModules.get(0).getModel());
        } else {
            List<CollectNode> collectNodes = collectNodeService.selectCollectNodeByCode(acquisitionMiddleware.getCode());
            if (collectNodes != null && collectNodes.size() > 0) {
                acquisitionMiddleware.setName(collectNodes.get(0).getModuleName());
                acquisitionMiddleware.setModuleModel(collectNodes.get(0).getModel());
            }
        }

        if (deviceEntity != null && deviceEntity.size() > 0) {
            acquisitionMiddleware.setDeviceStatus(1);
        } else {
            acquisitionMiddleware.setDeviceStatus(0);
        }
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(acquisitionMiddleware), action, "acquisitionMiddleware");
        KafkaMessage kafkaMessage = new KafkaMessage("cjzd", JSONObject.toJSONString(syncEntity), 30, System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 定时同步相关数据到岸端
     * @param syncAllEntity
     * @param action
     */
    public void syncAllNewShore(SyncAllEntity syncAllEntity, int action) {
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(syncAllEntity), action, "syncAll");
        KafkaMessage kafkaMessage = new KafkaMessage("046B", JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 定时同步船舶终端管理信息到岸端
     * @param shipTerminalManage
     * @param action
     */
    public void syncManage2Shore(ShipTerminalManage shipTerminalManage, int action) {
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(shipTerminalManage), action, "syncManage");
        KafkaMessage kafkaMessage = new KafkaMessage("101Z", JSONObject.toJSONString(syncEntity), 20, System.currentTimeMillis());
        storeService.sendSync2Kafka(kafkaMessage);
    }


}
