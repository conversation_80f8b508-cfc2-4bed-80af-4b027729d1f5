package com.xhjt.project.collect.service;

import com.xhjt.common.SerialPortUtil;
import com.xhjt.common.utils.MapUtil;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.framework.serialport.RtxtService;
import com.xhjt.project.collect.domain.CmdVo;
import com.xhjt.project.collect.domain.CollectModule;
import com.xhjt.project.collect.domain.CollectNode;
import gnu.io.SerialPort;
import gnu.io.SerialPortEvent;
import gnu.io.SerialPortEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 写入配置
 *
 * <AUTHOR>
 */
@Service
public class WriteCmdService {
    private Logger logger = LoggerFactory.getLogger(WriteCmdService.class);

    private static Map<String, String> listenTempMap = new ConcurrentHashMap<>();

    private static Map<String, Integer> sendTempMap = new ConcurrentHashMap<>();

    private static Map<Integer, CmdVo> cmdTempMap = new ConcurrentHashMap<>();

    private static Map<String, SerialPort> serialPortTempMap = new ConcurrentHashMap<>();

    private static String Subnet_Mask = "255.255.255.0";

    /**
     * 波特率
     */
    private static Integer baudRateTemp;

    /**
     * 数据位
     */
    private static Integer dataBitsTemp;

    /**
     * 停止位
     */
    private static Integer stopBitsTemp;

    /**
     * 校验位
     */
    private static Integer parityTemp;

    /**
     * 采集节点-配置入口
     * @param collectNode
     * @throws Exception
     */
    public void writeConfig(CollectNode collectNode) throws Exception {
        List<Integer> integers = integers = new ArrayList<Integer>(Arrays.asList(31, 32, 33,34,40,80,81));
        if (collectNode.getIsCollect() == 1) {
            //开启采集传输时
            baudRateTemp = collectNode.getBaudRate();
            dataBitsTemp = collectNode.getDataBits();
            stopBitsTemp = collectNode.getStopBits();
            parityTemp = collectNode.getParity();
        }
        if (collectNode.getModel().equalsIgnoreCase("XH-RS2020WS-2")){
            //暂定为旧得
            List<CmdVo> cmdVoList = initCmdList(collectNode,"&&&000");
            writeConfig(cmdVoList, collectNode.getSerialPort(),integers);
         }else if (collectNode.getModel().equalsIgnoreCase("XH-NEWWS-2")){
            List<CmdVo> cmdVoList = initCmdList(collectNode,"&&&001");
            writeConfig(cmdVoList, collectNode.getSerialPort(),integers);
        }

    }

    /**
     * 采集模块-配置入口
     * @param collectModule
     * @throws Exception
     */
    public void writeConfig(CollectModule collectModule,CollectNode collectNode) throws Exception {
        List<Integer> integers = new ArrayList<Integer>(Arrays.asList(41, 42, 43,44,50,80,81,82));
        baudRateTemp = collectModule.getBaudRate();
        dataBitsTemp = collectModule.getDataBits();
        stopBitsTemp = collectModule.getStopBits();
        parityTemp = collectModule.getParity();
        List<CmdVo> cmdVoList = new ArrayList<>();
        //这边需要根据模块型号进行判断走哪个模型初始化
        if (collectModule.getModel().equalsIgnoreCase("XH-RS2020WS-2")){
            //暂定为旧得
            cmdVoList = initEW11CmdList(collectModule,collectNode,"&&&000");
        }else if (collectModule.getModel().equalsIgnoreCase("XH-NEWWS-2")){
            cmdVoList = initEW11CmdList(collectModule,collectNode,"&&&001");
        }else{
            cmdVoList = initNewModuleCmdList(collectModule,collectNode);
        }
        writeConfig(cmdVoList, collectModule.getSerialPort(),integers);
    }

    private void writeConfig(List<CmdVo> cmdVoList, String serialPortName,List<Integer> integers) throws Exception {
        cmdTempMap = MapUtil.transListToMap(cmdVoList, "id", Integer.class);

        // 串口调通
        SerialPort serialPort = null;
        try {
            serialPort = connectSerialPort(serialPortName);
        } catch (InterruptedException e) {
            logger.error("连接的串口无法接通,{}", e);
            SerialPortUtil.closeComPort(serialPort);
            throw new Exception("连接的串口无法接通");
        }

        // 串口调通后，写配置
        try {
            sendCmd(integers.get(5),integers);
        } catch (Exception e) {
            logger.error("串口写配置错误,{}", e);
            throw new Exception(e.getMessage());
        } finally {
            SerialPortUtil.closeComPort(serialPort);
        }
    }

    /**
     * 串口发送命令
     *
     * @param id
     * @throws Exception
     */
    private void sendCmd(int id,List<Integer> integers) throws Exception {
        SerialPort serialPort = serialPortTempMap.get("SERIAL_PORT_TEMP");
        sendTempMap.put("SEND_ID", id);
        logger.info("串口发配置测试-000--送命令：{}---serialPort--{}", cmdTempMap.get(id).getSendStr(), serialPort.getBaudRate());
        SerialPortUtil.sendDataToComPort(serialPort, cmdTempMap.get(id).getSendStr().getBytes());
        //兼容采集模块
        if (id == integers.get(4)) {
            return;
        }
        boolean b = getListenBack(0, cmdTempMap.get(id).getWaitNum());
        if (id == integers.get(0)) {
            SerialPortUtil.closeComPort(serialPort);
            openSerialPort(serialPort.getName(), baudRateTemp, serialPort.getDataBits(), serialPort.getStopBits(), serialPort.getParity());
        }
        if (id == integers.get(1)) {
            SerialPortUtil.closeComPort(serialPort);
            openSerialPort(serialPort.getName(), serialPort.getBaudRate(), dataBitsTemp, serialPort.getStopBits(), serialPort.getParity());
        }
        if (id == integers.get(2)) {
            SerialPortUtil.closeComPort(serialPort);
            openSerialPort(serialPort.getName(), serialPort.getBaudRate(), serialPort.getDataBits(), stopBitsTemp, serialPort.getParity());
        }
        if (id == integers.get(3)) {
            SerialPortUtil.closeComPort(serialPort);
            openSerialPort(serialPort.getName(), serialPort.getBaudRate(), serialPort.getDataBits(), serialPort.getStopBits(), parityTemp);
        }
        if (!b) {
            throw new Exception("命令[" + cmdTempMap.get(id).getSendStr() + "]发送出错");
        }

        // 发送下一步命令
        if (cmdTempMap.get(id).getTrueNextId() == integers.get(0) && serialPort.getBaudRate() == baudRateTemp) {
            id = integers.get(0);
        }
        if (cmdTempMap.get(id).getTrueNextId() == integers.get(1) && serialPort.getDataBits() == dataBitsTemp) {
            id = integers.get(1);
        }
        if (cmdTempMap.get(id).getTrueNextId() == integers.get(2) && serialPort.getStopBits() == stopBitsTemp) {
            id = integers.get(2);
        }
        if (cmdTempMap.get(id).getTrueNextId() == integers.get(3) && serialPort.getParity() == parityTemp) {
            id = integers.get(3);
        }

        sendCmd(cmdTempMap.get(id).getTrueNextId(),integers);
    }

    /**
     * 连接串口
     *
     * @param portName
     * @return
     * @throws InterruptedException
     */
    private SerialPort connectSerialPort(String portName) throws InterruptedException {
        String sourcePortName = RtxtService.getOldSerialPort(portName);

        List<List<Integer>> descartes = new ArrayList<>();
        List<List<Integer>> configList = new ArrayList<>();
        //parity
        configList.add(Arrays.asList(0, 1, 2));
        //stopBits
        configList.add(Arrays.asList(1, 2));
        //dataBit
        configList.add(Arrays.asList(8, 7));
        //baudRate
        configList.add(Arrays.asList(9600, 4800, 19200, 115200, 38400, 57600, 2400, 230400));
        descartesRecursive(configList, 0, descartes, new ArrayList<Integer>());

        return connectSerialPort(sourcePortName, descartes, 0);
    }

    /**
     * 通过向串口发送数据并监听返回值，判断返回值是预期的值则表示配置正确
     *
     * @param portName
     * @param configList
     * @param num
     * @return
     * @throws InterruptedException
     */
    private SerialPort connectSerialPort(String portName, List<List<Integer>> configList, int num) throws InterruptedException {
        SerialPort serialPort = SerialPortUtil.openComPort(portName, configList.get(num).get(3), configList.get(num).get(2), configList.get(num).get(1), configList.get(num).get(0));
        if (serialPort == null) {
            return connectSerialPort(portName, configList, num + 1);
        }
        // 设置串口监听
        listenPort(serialPort);

        sendTempMap.put("SEND_ID", cmdTempMap.get(1).getId());
        SerialPortUtil.sendDataToComPort(serialPort, cmdTempMap.get(1).getSendStr().getBytes());
        boolean b = getListenBack(0, cmdTempMap.get(1).getWaitNum());

        logger.info("串口配置测试--333-发送状态：{}--", b);
        if (b) {
            serialPortTempMap.put("SERIAL_PORT_TEMP", serialPort);
            return serialPort;
        } else if (!b && num == configList.size() - 1) {
            return null;
        } else {
            SerialPortUtil.closeComPort(serialPort);
            return connectSerialPort(portName, configList, num + 1);
        }
    }

    private void listenPort(SerialPort serialPort) {
        SerialPortUtil.setListenerToSerialPort(serialPort, new SerialPortEventListener() {
            @Override
            public void serialEvent(SerialPortEvent arg0) {
                byte[] bytes;
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < 90; i++) {
                    bytes = SerialPortUtil.readData(serialPort, 50);
                    sb.append(new String(bytes, StandardCharsets.UTF_8));
                    logger.info("串口配置测试-111--发送命令：{}---返回值：{}", sendTempMap.get("SEND_ID"), sb.toString());

                    if (sendTempMap.get("SEND_ID") == 80 || sendTempMap.get("SEND_ID") == 81 ||sendTempMap.get("SEND_ID") == 82) {
                        listenTempMap.put("SERIAL_PORT_BACK", "true");
                        return;
                    }
                    if (StringUtils.isNotBlank(sb.toString()) && sendTempMap.get("SEND_ID") == 1
                            && (sb.toString().contains(cmdTempMap.get(sendTempMap.get("SEND_ID")).getBackStr()) || sb.toString().contains("+++"))) {
                        listenTempMap.put("SERIAL_PORT_BACK", "true");
                        return;
                    }
                    if (StringUtils.isNotBlank(sb.toString()) && sb.toString().contains(cmdTempMap.get(sendTempMap.get("SEND_ID")).getBackStr())) {
                        listenTempMap.put("SERIAL_PORT_BACK", "true");
                        return;
                    }
                }
                listenTempMap.put("SERIAL_PORT_BACK", "false");
            }
        });
    }

    private void openSerialPort(String portName, Integer baudRate, Integer dataBits, Integer stopBits, Integer parity) throws InterruptedException {
        SerialPort serialPort = SerialPortUtil.openComPort(portName, baudRate, dataBits, stopBits, parity);
        if (serialPort == null) {
            return;
        }
        serialPortTempMap.put("SERIAL_PORT_TEMP", serialPort);
        // 设置串口监听
        listenPort(serialPort);
        Thread.sleep(7000);
    }

    /**
     * 睡眠等待监听的数据
     *
     * @param n
     * @return
     * @throws InterruptedException
     */
    private Boolean getListenBack(int n, int all) throws InterruptedException {
        List<Integer> specialArr = Arrays.asList(31, 32, 33, 34, 80, 81,82,41,42,43,44);

        String s = listenTempMap.get("SERIAL_PORT_BACK");
        logger.info("串口配置测试--222-监听返回值：{}---次数：{}", s, n);
        if (specialArr.contains(sendTempMap.get("SEND_ID")) && (n >= 20 || StringUtils.isNotBlank(s))) {
            listenTempMap.remove("SERIAL_PORT_BACK");
            return true;
        }
        if (s == null) {
            Thread.sleep(50);
            if (n >= all) {
                return false;
            }
            return getListenBack(n + 1, all);
        }
        listenTempMap.remove("SERIAL_PORT_BACK");
        return "true".equals(s);
    }

    /**
     * 递归实现
     * 原理：从原始list的0开始依次遍历到最后
     *
     * @param originalList 原始list
     * @param position     当前递归在原始list的position
     * @param returnList   返回结果
     * @param cacheList    临时保存的list
     */
    private static void descartesRecursive(List<List<Integer>> originalList, int position, List<List<Integer>> returnList, List<Integer> cacheList) {
        List<Integer> originalItemList = originalList.get(position);
        for (int i = 0; i < originalItemList.size(); i++) {
            //最后一个复用cacheList，节省内存
            List<Integer> childCacheList = (i == originalItemList.size() - 1) ? cacheList : new ArrayList<>(cacheList);
            childCacheList.add(originalItemList.get(i));
            //遍历到最后退出递归
            if (position == originalList.size() - 1) {
                returnList.add(childCacheList);
                continue;
            }
            descartesRecursive(originalList, position + 1, returnList, childCacheList);
        }
    }

    public String getLanGatewayBySsid(String ssid) {
        if (StringUtils.isBlank(ssid) || !ssid.contains("_")) {
            return "";
        }
        String[] arr = ssid.split("_");
        return "20.20." + (200 + Integer.valueOf(arr[1])) + ".254";
    }

    /**
     * 获取IP地址
     * @param ssid
     * @return
     */
    public String getSubNetMaskBySsid(String ssid,Integer orderNum) {
        if (StringUtils.isBlank(ssid) || !ssid.contains("_")) {
            return "";
        }
        String[] arr = ssid.split("_");
        return "20.20." + (200 + Integer.valueOf(arr[1])) + "."+orderNum;
    }

    /**
     * 获取节点-本地端口
     * @param orderNum
     * @return
     */
    public Integer getPortByNum(Integer orderNum) {
        if (orderNum == null){
            return null;
        }
        return 40000 + orderNum;
    }

    /**
     * 采集节点-初始化命令
     *
     * @param collectNode
     * @return
     */
    private List<CmdVo> initCmdList(CollectNode collectNode,String title) {
        List<CmdVo> list = new ArrayList<>();

        CmdVo cmdVo = new CmdVo();
        cmdVo.setId(80);
        cmdVo.setSendStr("Exit\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(81);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(81);
        cmdVo.setSendStr("Exit\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(1);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(1);
        cmdVo.setSendStr("+++");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(2);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(2);
        cmdVo.setSendStr("SYS Network\r");
        cmdVo.setBackStr("EPORT/SYS/Network>");
        cmdVo.setTrueNextId(3);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(3);
        cmdVo.setSendStr("Mode Router\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(4);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(4);
        cmdVo.setSendStr("EthMode WAN\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(5);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(5);
        if (collectNode.getNetDhcp() == 0) {
            cmdVo.setSendStr("DHCP Disable\r");
            cmdVo.setBackStr("DHCP Disable");
            cmdVo.setTrueNextId(6);
        } else {
            cmdVo.setSendStr("DHCP Enable\r");
            cmdVo.setBackStr("SET-OK");
            cmdVo.setTrueNextId(10);
        }
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(6);
        cmdVo.setSendStr(collectNode.getNetIp() + "\r");
        cmdVo.setBackStr(collectNode.getNetIp());
        cmdVo.setTrueNextId(7);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(7);
        cmdVo.setSendStr(collectNode.getNetSubnetMask() + "\r");
        cmdVo.setBackStr(collectNode.getNetSubnetMask());
        cmdVo.setTrueNextId(8);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(8);
        cmdVo.setSendStr(collectNode.getNetGateway() + "\r");
        cmdVo.setBackStr(collectNode.getNetGateway());
        cmdVo.setTrueNextId(10);
        list.add(cmdVo);

//        cmdVo = new CmdVo();
//        cmdVo.setId(9);
//        cmdVo.setSendStr("Lan " + getLanGatewayBySsid(collectNode.getWifiSsid()) + " "+Subnet_Mask+" Disable\r");
//        cmdVo.setBackStr("SET-OK");
//        cmdVo.setTrueNextId(10);
//        cmdVo.setWaitNum(100);
//        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(10);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT/SYS>");
        cmdVo.setTrueNextId(11);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(11);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(12);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(12);
        cmdVo.setSendStr("WIFI\r");
        cmdVo.setBackStr("EPORT/WIFI>");
        cmdVo.setTrueNextId(13);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(13);
        cmdVo.setSendStr("Mode AP\r");
        cmdVo.setBackStr("Input AP SSID");
        cmdVo.setTrueNextId(15);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(15);
        cmdVo.setSendStr(collectNode.getWifiSsid() + "\r");
        cmdVo.setBackStr("Input AP Key");
        cmdVo.setTrueNextId(16);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(16);
        cmdVo.setSendStr(collectNode.getWifiPassword() + "\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(17);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(17);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        if (collectNode.getIsCollect() == 0) {
            cmdVo.setTrueNextId(40);
        } else {
            cmdVo.setTrueNextId(18);
        }
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(18);
        cmdVo.setSendStr("SOCK netp\r");
        cmdVo.setBackStr("EPORT/SOCK/netp>");
        cmdVo.setTrueNextId(19);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(19);
        cmdVo.setSendStr("Proto TCP-CLIENT\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(20);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(20);
        cmdVo.setSendStr("Server " + collectNode.getShipIp() + "\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(21);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(21);
        cmdVo.setSendStr("ServerPort 22333\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(22);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(22);
        cmdVo.setSendStr("Register Enable\r");
        cmdVo.setBackStr("Input regPkt:");
        cmdVo.setTrueNextId(23);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(23);
        cmdVo.setSendStr(title+"#" + collectNode.getCode() + "#%MAC#12345678901234567890@@\r");
        cmdVo.setBackStr("Input RegPktSendType:");
        cmdVo.setTrueNextId(24);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(24);
        cmdVo.setSendStr("Both\r");
        cmdVo.setBackStr("EPORT/SOCK/netp>");
        cmdVo.setTrueNextId(25);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(25);
        cmdVo.setSendStr("Save\r");
        cmdVo.setBackStr("===SOCK Config===");
        cmdVo.setTrueNextId(26);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(26);
        cmdVo.setSendStr("Y\r");
        cmdVo.setBackStr("EPORT/SOCK/netp>");
        cmdVo.setTrueNextId(27);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(27);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT/SOCK>");
        cmdVo.setTrueNextId(28);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(28);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        if (collectNode.getIsCollect() == 1) {
            cmdVo.setTrueNextId(30);
        } else {
            cmdVo.setTrueNextId(40);
        }
        list.add(cmdVo);

        if (collectNode.getIsCollect() == 1) {
            cmdVo = new CmdVo();
            cmdVo.setId(30);
            cmdVo.setSendStr("UART\r");
            cmdVo.setBackStr("EPORT/UART>");
            cmdVo.setTrueNextId(31);
            list.add(cmdVo);

            cmdVo = new CmdVo();
            cmdVo.setId(31);
            cmdVo.setSendStr("Baudrate " + collectNode.getBaudRate() + "\r");
            cmdVo.setBackStr("SET-OK");
            cmdVo.setTrueNextId(32);
            list.add(cmdVo);

            cmdVo = new CmdVo();
            cmdVo.setId(32);
            cmdVo.setSendStr("Databits " + collectNode.getDataBits() + "\r");
            cmdVo.setBackStr("SET-OK");
            cmdVo.setTrueNextId(33);
            list.add(cmdVo);

            cmdVo = new CmdVo();
            cmdVo.setId(33);
            cmdVo.setSendStr("Stopbits " + collectNode.getStopBits() + "\r");
            cmdVo.setBackStr("SET-OK");
            cmdVo.setTrueNextId(34);
            list.add(cmdVo);

            cmdVo = new CmdVo();
            cmdVo.setId(34);
            String p = "";
            if (collectNode.getParity() == 0) {
                p = "NONE";
            } else if (collectNode.getParity() == 1) {
                p = "ODD";
            } else if (collectNode.getParity() == 2) {
                p = "EVEN";
            }
            cmdVo.setSendStr("Parity " + p + "\r");
            cmdVo.setBackStr("SET-OK");
            cmdVo.setTrueNextId(35);
            list.add(cmdVo);
        }

        cmdVo = new CmdVo();
        cmdVo.setId(35);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(36);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(36);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(40);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(40);
        cmdVo.setSendStr("Restart\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(40);
        list.add(cmdVo);

        return list;
    }
    /**
     * 采集模块-初始化命令-旧模块EW11
     * @param collectModule
     * @param collectNode
     * @return
     */
    private List<CmdVo> initEW11CmdList(CollectModule collectModule, CollectNode collectNode,String title) {
        List<CmdVo> list = new ArrayList<>();
        CmdVo cmdVo = new CmdVo();
        cmdVo.setId(80);
        cmdVo.setSendStr("Exit\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(81);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(81);
        cmdVo.setSendStr("Exit\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(82);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(82);
        cmdVo.setSendStr("Exit\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(1);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(1);
        cmdVo.setSendStr("+++");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(2);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(2);
        cmdVo.setSendStr("WIFI\r");
        cmdVo.setBackStr("EPORT/WIFI>");
        cmdVo.setTrueNextId(3);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(3);
        cmdVo.setSendStr("Mode STA\r");
        cmdVo.setBackStr("Input STA SSID");
        cmdVo.setTrueNextId(4);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(4);
        cmdVo.setSendStr(collectNode.getWifiSsid() + "\r");//xh001
        cmdVo.setBackStr("Input STA Key");
        cmdVo.setTrueNextId(5);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(5);
        cmdVo.setSendStr(collectNode.getWifiPassword()+ "\r");//12345678
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(6);

        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(6);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(10);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(10);
        cmdVo.setSendStr("SYS Network\r");
        cmdVo.setBackStr("EPORT/SYS/Network>");
        cmdVo.setTrueNextId(11);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(11);
        cmdVo.setSendStr("DHCP Enable\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(15);
        list.add(cmdVo);

//        cmdVo = new CmdVo();
//        cmdVo.setId(12);
//        cmdVo.setSendStr(getSubNetMaskBySsid(collectNode.getWifiSsid(),collectModule.getNodeNum()) + "\r");
//        cmdVo.setBackStr("Input SubNetMask");
//        cmdVo.setTrueNextId(13);
//        list.add(cmdVo);
//
//        cmdVo = new CmdVo();
//        cmdVo.setId(13);
//        cmdVo.setSendStr(Subnet_Mask+"\r");
//        cmdVo.setBackStr("Input Gateway");
//        cmdVo.setTrueNextId(14);
//        list.add(cmdVo);
//
//        cmdVo = new CmdVo();
//        cmdVo.setId(14);
//        cmdVo.setSendStr(getLanGatewayBySsid(collectNode.getWifiSsid()) + "\r");
//        cmdVo.setBackStr("SET-OK");
//        cmdVo.setTrueNextId(15);
//        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(15);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT/SYS>");
        cmdVo.setTrueNextId(16);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(16);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(20);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(20);
        cmdVo.setSendStr("SOCK netp\r");
        cmdVo.setBackStr("EPORT/SOCK/netp>");
        cmdVo.setTrueNextId(21);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(21);
        cmdVo.setSendStr("Proto TCP-CLIENT\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(22);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(22);
        cmdVo.setSendStr("Server " + collectModule.getShipIp() + "\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(23);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(23);
        cmdVo.setSendStr("ServerPort 22333\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(24);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(24);
        cmdVo.setSendStr("LocalPort "+getPortByNum(collectModule.getNodeNum())+"\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(25);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(25);
        cmdVo.setSendStr("RegPktCode On "+title+"#" + collectModule.getCode() + "#%MAC#12345678901234567890@@\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(26);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(26);
        cmdVo.setSendStr("SendRegPktMode Both\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(27);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(27);
        cmdVo.setSendStr("Save\r");
        cmdVo.setBackStr("===SOCK Config===");
        cmdVo.setTrueNextId(28);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(28);
        cmdVo.setSendStr("Y\r");
        cmdVo.setBackStr("EPORT/SOCK/netp>");
        cmdVo.setTrueNextId(29);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(29);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT/SOCK>");
        cmdVo.setTrueNextId(30);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(30);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(40);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(40);
        cmdVo.setSendStr("UART\r");
        cmdVo.setBackStr("EPORT/UART>");
        cmdVo.setTrueNextId(41);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(41);
        cmdVo.setSendStr("Baudrate " + collectModule.getBaudRate() + "\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(42);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(42);
        cmdVo.setSendStr("Databits " + collectModule.getDataBits() + "\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(43);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(43);
        cmdVo.setSendStr("Stopbits " + collectModule.getStopBits() + "\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(44);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(44);
        String p = "";
        if (collectModule.getParity() == 0) {
            p = "NONE";
        } else if (collectModule.getParity() == 1) {
            p = "ODD";
        } else if (collectModule.getParity() == 2) {
            p = "EVEN";
        }
        cmdVo.setSendStr("Parity " + p + "\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(45);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(45);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(46);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(46);
        cmdVo.setSendStr("Quit\r");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(50);
        cmdVo.setSendStr("Restart\r");
        cmdVo.setBackStr("SET-OK");
        cmdVo.setTrueNextId(50);
        list.add(cmdVo);
        return list;
    }
    /**
     * 采集模块-初始化命令-新模块
     * @param collectModule
     * @param collectNode
     * @return
     */
    private List<CmdVo> initNewModuleCmdList(CollectModule collectModule,CollectNode collectNode) {
        List<CmdVo> list = new ArrayList<>();

        CmdVo cmdVo = new CmdVo();
        cmdVo.setId(80);
        cmdVo.setSendStr("AT+ENTM<CR>\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(81);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(81);
        cmdVo.setSendStr("AT+ENTM<CR>\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(82);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(82);
        cmdVo.setSendStr("AT+ENTM<CR>\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(1);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(1);
        cmdVo.setSendStr("+++");
        cmdVo.setBackStr("EPORT>");
        cmdVo.setTrueNextId(2);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(2);
        cmdVo.setSendStr("a"+ "\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(3);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(3);
        cmdVo.setSendStr("AT+WMODE=STA"+ "\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(4);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(4);
        cmdVo.setSendStr("AT+WSSSID="+collectNode.getWifiSsid()+ "\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(5);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(5);
        cmdVo.setSendStr("AT+ WSKEY=WPA2PSK,AES,"+collectNode.getWifiPassword()+ "\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(6);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(6);
        cmdVo.setSendStr("AT+WANN=static,"+getSubNetMaskBySsid(collectNode.getWifiSsid(),collectModule.getNodeNum())+","+Subnet_Mask+","+getLanGatewayBySsid(collectNode.getWifiSsid())+"\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(7);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(7);
        cmdVo.setSendStr("AT+NETP =TCP,CLIENT,22333,"+collectNode.getShipIp()+"\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(8);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(8);
        cmdVo.setSendStr("AT+NREGSND = A,both\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(9);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(9);
        cmdVo.setSendStr("AT+NREGDT = A,&&&000#" + collectModule.getCode() + "#%MAC#12345678901234567890@@\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(10);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(10);
        cmdVo.setSendStr("AT+NREGEN=A,on\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(11);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(11);
        cmdVo.setSendStr("AT+UART=115200,8,1,None,NFC\r");
        cmdVo.setBackStr("+ok");
        cmdVo.setTrueNextId(50);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        cmdVo = new CmdVo();
        cmdVo.setId(50);
        cmdVo.setSendStr("AT+Z\r");
        cmdVo.setBackStr("");
        cmdVo.setTrueNextId(50);
        cmdVo.setFalseNextId(50);
        list.add(cmdVo);

        return list;
    }
}
