package com.xhjt.project.device.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**采集-传输数据收集记录表
 * Created by ch<PERSON>mingyong on 2021/10/18.
 */
public class DataStatistics {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 设备类型（0-设备，1-快照）
     */
    private Integer deviceType;

    /**
     * 数据类型（0-采集数据，1-传输数据）
     */
    private Integer dataType;

    /**
     * 字符长度
     */
    private Integer characterLength;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Integer getCharacterLength() {
        return characterLength;
    }

    public void setCharacterLength(Integer characterLength) {
        this.characterLength = characterLength;
    }
}
