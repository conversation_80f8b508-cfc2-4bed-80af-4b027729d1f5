package com.xhjt.project.snapshot.api;

import com.alibaba.fastjson.JSON;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.project.snapshot.service.SnapshotTransferService;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/snapshot/transfer")
public class SnapshotTransferApiController extends BaseController {

    @Autowired
    private SnapshotTransferService snapshotTransferService;

    /**
     * 修改设备传输属性
     *
     * @param jsonObject
     */
    @PostMapping("/updateBySync")
    public void updateBySync(String jsonObject) {
        try {
            SyncEntity syncEntity = JSON.parseObject(jsonObject, SyncEntity.class);
            if (syncEntity == null) {
                return;
            }
            snapshotTransferService.handleBySync(syncEntity);
            SnapshotUtil.addAllInfo();
        } catch (Exception e) {
            logger.error("---{}", e);
        }
    }

    /**
     * 修改快照通道及传输属性
     *
     * @param jsonObject
     */
    @PostMapping("/updateSnapTransfer2BySync")
    public void updateSnapTransfer2BySync(String jsonObject) {
        try {
            SyncEntity syncEntity = JSON.parseObject(jsonObject, SyncEntity.class);
            if (syncEntity == null) {
                return;
            }
            snapshotTransferService.handleAllBySync(syncEntity);
            SnapshotUtil.addAllInfo();
        } catch (Exception e) {
            logger.error("---{}", e);
        }
    }

}
