package com.xhjt.project.device.domain;


import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 采集中间件连接日志
 *
 * <AUTHOR>
 */
public class ConnectLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 机器MAC地址
     */
    private String mac;

    /**
     * 名称
     */
    private String name;

    /**
     * 设备外网IP
     */
    private String ip;

    /**
     * 设备开放端口
     */
    private Integer port;

    /**
     * 动作类型(1连接 2断开 )
     */
    private Integer action;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

}
