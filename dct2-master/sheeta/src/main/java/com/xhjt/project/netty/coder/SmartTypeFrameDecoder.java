package com.xhjt.project.netty.coder;

import com.xhjt.common.utils.StringUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.handler.codec.TooLongFrameException;
import io.netty.util.ByteProcessor;
import io.netty.util.CharsetUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * class
 *
 * <AUTHOR>
 */
public class SmartTypeFrameDecoder extends ByteToMessageDecoder {
    protected Logger logger = LoggerFactory.getLogger(SmartTypeFrameDecoder.class);
    private static final Integer MSG_MIN_LENGTH = 48;
    private static final Integer MSG_LENGTH_LENGTH = 48;

    /**
     * Maximum length of a frame we're willing to decode.
     */
    private final int maxLength;
    /**
     * Whether or not to throw an exception as soon as we exceed maxLength.
     */
    private final boolean failFast;
    private final boolean stripDelimiter;

    /**
     * True if we're discarding input because we're already over maxLength.
     */
    private boolean discarding;
    private int discardedBytes;

    private final ByteBuf delimiter;

    /**
     * 默认构造器，ByteBuf 可能会无限扩容
     * ByteBuf 超过1024之后，会清除已读区域
     */
    public SmartTypeFrameDecoder(final int maxLength) {
        this(maxLength, true, false);
    }

    /**
     * Creates a new decoder.
     *
     * @param maxLength      the maximum length of the decoded frame.
     *                       A {@link TooLongFrameException} is thrown if
     *                       the length of the frame exceeds this value.
     * @param stripDelimiter whether the decoded frame should strip out the
     *                       delimiter or not
     * @param failFast       If <tt>true</tt>, a {@link TooLongFrameException} is
     *                       thrown as soon as the decoder notices the length of the
     *                       frame will exceed <tt>maxFrameLength</tt> regardless of
     *                       whether the entire frame has been read.
     *                       If <tt>false</tt>, a {@link TooLongFrameException} is
     *                       thrown after the entire frame that exceeds
     *                       <tt>maxFrameLength</tt> has been read.
     */
    public SmartTypeFrameDecoder(final int maxLength, final boolean stripDelimiter, final boolean failFast) {
        this.maxLength = maxLength;
        this.failFast = failFast;
        this.stripDelimiter = stripDelimiter;
        this.delimiter = Unpooled.copiedBuffer("&&&".getBytes());
    }

    @Override
    protected final void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
         Object decoded = decode(ctx, in);
        if (decoded != null) {
            out.add(decoded);
        }

    }
    /**
     * 字节数组转16进制
     */
    public String bytesToHex(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF).toUpperCase();
            if (hex.length() < 2) {
                sb.append(0);
            }
            sb.append(hex + " ");
        }
        return sb.toString();
    }
    /**
     * 解码消息
     *
     * @param ctx
     * @param buffer
     * @throws Exception
     */
    protected Object decode(ChannelHandlerContext ctx, ByteBuf buffer) {
        clearRead(buffer);
        logger.info("原始数据---decode--{}",buffer.toString(StandardCharsets.UTF_8));
        // 消息小于接收的最小长度
        if (buffer.readableBytes() < MSG_MIN_LENGTH) {
            return null;
        }
        // 记录消息头部位置
        int headIndex = indexOf(buffer, delimiter);
        int beginIndex = headIndex > buffer.readerIndex() ? headIndex : buffer.readerIndex();

        // 没有包含头部信息，直接丢弃
        if (beginIndex < 0) {
            buffer.readerIndex(buffer.writerIndex());
            return null;
        }
        if (beginIndex > 0) {
            buffer.readerIndex(beginIndex);
        }
        //消息长度
        if (buffer.readableBytes() < MSG_LENGTH_LENGTH) {
            return null;
        }
        ByteBuf buf = buffer.readBytes(6);
        buffer.readerIndex(beginIndex);
        if ("&&&001".equals(buf.toString(CharsetUtil.UTF_8))) {
            return buffer.readRetainedSlice(buffer.readableBytes());
        }else {
            final int eol = findEndOfLine(buffer);
            if (!discarding) {
                if (eol >= 0) {
                    final ByteBuf frame;
                    final ByteBuf res;
                    final int length = eol - buffer.readerIndex();
                    final int delimLength = buffer.getByte(eol) == '\r' ? 2 : 1;
                    if (length > maxLength) {
                        buffer.readerIndex(eol + delimLength);
                        fail(ctx, length);
                        return null;
                    }
                    if (stripDelimiter) {
                        frame = buffer.readRetainedSlice(length);
                        String result = frame.toString(StandardCharsets.UTF_8);

                        if (result.lastIndexOf("@@") > -1) {
                            int i = result.lastIndexOf("@@") - 46;
                            String resultStr = result.substring(i);
                            ByteBuf bufResult = Unpooled.copiedBuffer(resultStr, CharsetUtil.UTF_8);
                            res = bufResult.readRetainedSlice(bufResult.readableBytes());
                            buffer.skipBytes(delimLength);
                        } else {
                            buffer.skipBytes(delimLength);
                            return frame;
                        }

                    } else {
                        frame = buffer.readRetainedSlice(length + delimLength);
                        String result = frame.toString(StandardCharsets.UTF_8);
                        if (result.lastIndexOf("@@") > -1) {
                            int i = result.lastIndexOf("@@") - 46;
                            String resultStr = result.substring(i);
                            ByteBuf bufResult = Unpooled.copiedBuffer(resultStr, CharsetUtil.UTF_8);
                            res = bufResult.readRetainedSlice(bufResult.readableBytes());
                        } else {
                            return frame;
                        }
                    }
                    return res;
                } else {
                    final int length = buffer.readableBytes();
                    if (length > maxLength) {
                        discardedBytes = length;
                        buffer.readerIndex(buffer.writerIndex());
                        discarding = true;
                        if (failFast) {
                            fail(ctx, "over " + discardedBytes);
                        }
                    }
                    return null;
                }
            } else {
                if (eol >= 0) {
                    final int length = discardedBytes + eol - buffer.readerIndex();
                    final int delimLength = buffer.getByte(eol) == '\r' ? 2 : 1;
                    buffer.readerIndex(eol + delimLength);
                    discardedBytes = 0;
                    discarding = false;
                    if (!failFast) {
                        fail(ctx, length);
                    }
                } else {
                    discardedBytes += buffer.readableBytes();
                    buffer.readerIndex(buffer.writerIndex());
                }
                return null;
            }
        }
    }

    /**
     * 清除 0 - readIndex 的数据，以免  ByteBuf 过大
     * 如果每一次消息最后，都带有一段解析不了的脏消息，或者有一段小于{@link #MSG_MIN_LENGTH} 的消息,这样每次都会有未读完的消息， 就可能导致 ByteBuf 无限扩容
     *
     * @param in
     */
    private void clearRead(ByteBuf in) {
        if (maxLength > 0 && in.writerIndex() > maxLength) {
            in.discardReadBytes();
        }

    }

    private void fail(final ChannelHandlerContext ctx, int length) {
        fail(ctx, String.valueOf(length));
    }

    private void fail(final ChannelHandlerContext ctx, String length) {
        ctx.fireExceptionCaught(
                new TooLongFrameException(
                        "frame length (" + length + ") exceeds the allowed maximum (" + maxLength + ')'));
    }

    /**
     * Returns the index in the buffer of the end of line found.
     * Returns -1 if no end of line was found in the buffer.
     */
    private static int findEndOfLine(final ByteBuf buffer) {
        int i = buffer.forEachByte(ByteProcessor.FIND_LF);
        if (i > 0 && buffer.getByte(i - 1) == '\r') {
            i--;
        }
        return i;
    }

    private static int indexOf(ByteBuf haystack, ByteBuf needle) {
        for (int i = haystack.readerIndex(); i < haystack.writerIndex(); i++) {
            int haystackIndex = i;
            int needleIndex;
            for (needleIndex = 0; needleIndex < needle.capacity(); needleIndex++) {
                if (haystack.getByte(haystackIndex) != needle.getByte(needleIndex)) {
                    break;
                } else {
                    haystackIndex++;
                    if (haystackIndex == haystack.writerIndex() &&
                            needleIndex != needle.capacity() - 1) {
                        return -1;
                    }
                }
            }

            if (needleIndex == needle.capacity()) {
                // Found the needle from the haystack!
                return i - haystack.readerIndex();
            }
        }
        return -1;
    }
}
