package com.xhjt.project.device.mapper;


import com.xhjt.project.device.domain.SerialConfig;

import java.util.List;

/**
 * 串口配置
 *
 * <AUTHOR>
 */
public interface SerialConfigMapper {

    /**
     * 查询串口配置
     *
     * @param serialConfig 串口配置
     * @return 串口配置
     */
    public List<SerialConfig> selectSerialConfigList(SerialConfig serialConfig);

    /**
     * 查询串口配置单个对象
     * @param serialConfig
     * @return
     */
    public SerialConfig selectSerialConfig(SerialConfig serialConfig);

    /**
     * 新增
     *
     * @param serialConfig 串口配置
     * @return 结果
     */
    public int addSerialConfig(SerialConfig serialConfig);

    /**
     * 删除串口配置
     *
     * @param id 参数ID
     * @return 结果
     */
    public int deleteSerialConfigById(Long id);

    /**
     * 批量删除串口配置
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    public int deleteSerialConfigByIds(Long[] ids);

    /**
     * 修改
     *
     * @param serialConfig 串口配置
     * @return 结果
     */
    public int updateSerialConfig(SerialConfig serialConfig);
}