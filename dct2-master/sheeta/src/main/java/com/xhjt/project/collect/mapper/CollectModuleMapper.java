package com.xhjt.project.collect.mapper;


import com.xhjt.project.collect.domain.CollectModule;

import java.util.List;

/**
 * 采集模块 数据层
 *
 * <AUTHOR>
 */
public interface CollectModuleMapper {
    /**
     * 查询采集模块信息
     *
     * @param collectModule 采集模块信息
     * @return 采集模块信息
     */
    public CollectModule selectCollectModule(CollectModule collectModule);

    /**
     * 查询采集模块列表
     *
     * @param collectModule 采集模块信息
     * @return 采集模块集合
     */
    public List<CollectModule> selectCollectModuleList(CollectModule collectModule);

    /**
     * 新增采集模块
     *
     * @param collectModule 采集模块信息
     * @return 结果
     */
    public int addCollectModule(CollectModule collectModule);

    /**
     * 修改采集模块
     *
     * @param collectModule 采集模块信息
     * @return 结果
     */
    public int updateCollectModule(CollectModule collectModule);

    /**
     * 删除采集模块
     *
     * @param collectModuleId 参数ID
     * @return 结果
     */
    public int deleteCollectModuleById(Long collectModuleId);

    /**
     * 批量删除参数信息
     *
     * @param collectModuleIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteCollectModuleByIds(Long[] collectModuleIds);

}
