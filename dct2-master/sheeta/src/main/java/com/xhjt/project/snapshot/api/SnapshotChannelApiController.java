package com.xhjt.project.snapshot.api;

import com.alibaba.fastjson.JSON;
import com.xhjt.common.ClientListernerUtil;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotHandlerService;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/snapshot/channel")
public class SnapshotChannelApiController extends BaseController {

    @Autowired
    private SnapshotChannelService snapshotChannelService;
    @Autowired
    private SnapshotHandlerService snapshotHandlerService;

    /**
     * 修改设备传输属性
     *
     * @param jsonObject
     */
    @PostMapping("/updateBySync")
    public void updateBySync(String jsonObject) {
        SyncEntity syncEntity = JSON.parseObject(jsonObject, SyncEntity.class);
        if (syncEntity == null) {
            return;
        }
        snapshotChannelService.handleBySync(syncEntity);
        SnapshotUtil.addAllInfo();
    }

    /**
     * 岸端快照预览
     *
     * @param jsonObject
     */
    @PostMapping("/snapshotViewBySync")
    public void snapshotViewBySync(String jsonObject) {
        SyncEntity syncEntity = JSON.parseObject(jsonObject, SyncEntity.class);
        if (syncEntity == null) {
            return;
        }
        try {
            logger.info("快照预览对象传到sheeta,开始进行截屏操作-----{}",jsonObject);
            snapshotHandlerService.startNew(syncEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 控制类操作
     *
     * @param jsonObject
     */
    @PostMapping("/controller")
    public void controller(String jsonObject) {
        try {
            //用于控制船端的操作，目前仅做一个重启机器操作
            ClientListernerUtil.reboot();
        } catch (Exception e) {
            logger.error("服务器重启失败");
            e.printStackTrace();
        }
    }

}
