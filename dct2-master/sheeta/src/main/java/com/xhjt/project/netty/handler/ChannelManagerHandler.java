package com.xhjt.project.netty.handler;

import com.xhjt.common.ClientListernerUtil;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.netty.common.AmUtil;
import com.xhjt.project.netty.common.DeviceUtil;
import com.xhjt.project.netty.domain.AmProtocol;
import com.xhjt.project.netty.service.StoreService;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ChannelManagerHandler extends ChannelInboundHandlerAdapter {

    protected Logger logger = LoggerFactory.getLogger(ChannelManagerHandler.class);
    public ClientListernerUtil clientListernerUtil = SpringUtils.getBean(ClientListernerUtil.class);
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        super.channelActive(ctx);
        try {
            String ip = ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress();
            Integer port = ((InetSocketAddress) ctx.channel().remoteAddress()).getPort();

            AmUtil.addAm(ip, port);

            logger.info("客户端连接成功：ip:{},port:{}", ip, port);
        } catch (Exception e) {
            logger.error("客户端连接失败，--{}", e);
            ctx.channel().close();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        String ip = ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress();
        Integer port = ((InetSocketAddress) ctx.channel().remoteAddress()).getPort();

        AmUtil.removeAm(ip, port);

        logger.info("客户端连接关闭：{}，ip:{}, Port:{}", ctx.channel().id(), ip, port);

        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        try {
            String ip = ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress();
            Integer port = ((InetSocketAddress) ctx.channel().remoteAddress()).getPort();
            AcquisitionMiddleware am = AmUtil.getAm(ip, port);

            if (am == null) {
                ctx.channel().close();
            }

            ByteBuf byteBuf = (ByteBuf) msg;

            AmProtocol amProtocol = new AmProtocol(byteBuf, am);
            if (StringUtils.isBlank(amProtocol.getMac())) {
                return;
            }
             //一分钟收集采集数据逻辑实现
            clientListernerUtil.redisCompare(amProtocol.getContent());
            //传输原始数据到pazu->再通过udp传输到岸端进行显示预览----20220224
            clientListernerUtil.syncAllRawData(amProtocol);
            // websocket发送实时数据
            if (ClientListernerUtil.states == 0 ){
                clientListernerUtil.webSocketSendMsg(amProtocol);
            }

            //新增的时候-解析预览接口
            if (ClientListernerUtil.states == 1 ) {
                clientListernerUtil.parseView(amProtocol);
            }
            // 禁用状态，不接收数据
            if (am.getEnable() == 0) {
//                logger.info("未启用状态，不接收数据11：--类型:{}, 数据:{}", amProtocol.getType(), new String(amProtocol.getContent(), StandardCharsets.UTF_8));
                return;
            }
            // 第一次接收到数据，状态为未知
            if (am.getEnable() == 2) {
                logger.info("第一次接收到数据，状态为未知222：数据:{}", new String(amProtocol.getContent(), StandardCharsets.UTF_8));
                am = AmUtil.renewAm(ip, port, amProtocol);
            }
            StoreService storeService = SpringUtils.getBean(StoreService.class);
            //2022---新的逻辑：一个采集终端可适配多个设备
            DeviceService deviceService = SpringUtils.getBean(DeviceService.class);
            DeviceEntity device1 = new DeviceEntity();
            device1.setMac(amProtocol.getMac());
            device1.setConnectType(0);//采集终端
            List<DeviceEntity> deviceEntitys = deviceService.selectDeviceList(device1);
            logger.info("接收数据33333--{}",deviceEntitys.size());
            if (deviceEntitys != null && deviceEntitys.size()> 0) {
                for (DeviceEntity device : deviceEntitys) {
                    //存储状态或者传输状态下进行往下走
                    if ((device.getEnable() == 1 || device.getTransferStatus() == 1) && device.getType()!=0) {
                        logger.info("接收数据,--类型:{}--接收数据：--{}", device.getType(), new String(amProtocol.getContent(), StandardCharsets.UTF_8));
                        String receiveMessage;

                        if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
                            // ATTITUDE
                            receiveMessage = bytesToTenString(amProtocol.getContent());
                        }else if (DeviceTypeEnum.GPS_IN.getValue().equals(device.getType()) || DeviceTypeEnum.COMPASS_IN.getValue().equals(device.getType())) {
                            //内置设备
                            receiveMessage = bytesTo16String(amProtocol.getContent());
                        } else {
                            receiveMessage = new String(amProtocol.getContent(), StandardCharsets.UTF_8);
                            if (receiveMessage.contains("@@")) {
                                receiveMessage = receiveMessage.substring(8);
                            }
                        }
                        if (receiveMessage.contains("\n\t")){
                            receiveMessage = receiveMessage.replace("\n","");
                            //删除\t
                            receiveMessage = receiveMessage.replace("\t","");
                        }
                        KafkaMessage kafkaMessage = new KafkaMessage(device.getType(), device.getCode(), receiveMessage, device.getCost(), amProtocol.getTime());

                        //发送数据到kafka
                        storeService.send2Kafka(kafkaMessage);

                        //存入txt文件
                        storeService.save2Txt(kafkaMessage);
                    }
                }
            }

        } finally {
            ReferenceCountUtil.release(msg);
        }
    }


    private static String bytesToTenString(byte[] byteArr) {
        StringBuilder sb = new StringBuilder(byteArr.length);
        String sTemp;
        for (byte b : byteArr) {
            sTemp = Integer.toHexString(0xFF & b);
            long decNum = Long.parseLong(sTemp, 16);
            sb.append(decNum).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    private static String bytesTo16String(byte[] src){
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString().toUpperCase();

    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        logger.error("客户端连接异常，--{}", cause.getMessage());
        // 当出现异常就关闭连接
        cause.printStackTrace();
        ctx.close();
    }




}
