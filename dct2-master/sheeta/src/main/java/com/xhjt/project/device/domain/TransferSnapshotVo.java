package com.xhjt.project.device.domain;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;

/**
 * Created by chen<PERSON><PERSON>g on 2021/10/23.
 */
public class TransferSnapshotVo {
    private SnapshotChannelEntity snapshotChannelEntity;
    private SnapshotTransferEntity snapshotTransferEntity;

    public SnapshotChannelEntity getSnapshotChannelEntity() {
        return snapshotChannelEntity;
    }

    public void setSnapshotChannelEntity(SnapshotChannelEntity snapshotChannelEntity) {
        this.snapshotChannelEntity = snapshotChannelEntity;
    }

    public SnapshotTransferEntity getSnapshotTransferEntity() {
        return snapshotTransferEntity;
    }

    public void setSnapshotTransferEntity(SnapshotTransferEntity snapshotTransferEntity) {
        this.snapshotTransferEntity = snapshotTransferEntity;
    }
}
