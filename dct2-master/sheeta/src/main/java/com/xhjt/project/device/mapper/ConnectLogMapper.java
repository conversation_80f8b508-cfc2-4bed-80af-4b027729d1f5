package com.xhjt.project.device.mapper;


import com.xhjt.project.device.domain.ConnectLog;

import java.util.List;

/**
 * 采集中间件日志数据层
 *
 * <AUTHOR>
 */
public interface ConnectLogMapper {

    /**
     * 查询设备列表
     *
     * @param connectLog 采集中间件日志
     * @return 设备集合
     */
    public List<ConnectLog> selectConnectLogList(ConnectLog connectLog);

    /**
     * 新增
     *
     * @param connectLog 采集中间件日志
     * @return 结果
     */
    public int addConnectLog(ConnectLog connectLog);

    /**
     * 删除
     *
     * @param id 参数ID
     * @return 结果
     */
    public int deleteConnectLogById(Long id);

    /**
     * 批量删除参数信息
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    public int deleteConnectLogByIds(Long[] ids);
}