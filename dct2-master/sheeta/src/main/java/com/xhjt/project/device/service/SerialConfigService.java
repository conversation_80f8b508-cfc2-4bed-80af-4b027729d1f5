package com.xhjt.project.device.service;

import com.xhjt.project.device.domain.SerialConfig;
import com.xhjt.project.device.mapper.SerialConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 串口名称配置
 *
 * <AUTHOR>
 */
@Service
public class SerialConfigService {

    @Autowired
    private SerialConfigMapper serialConfigMapper;


    /**
     * 查询串口名称配置
     *
     * @param serialConfig
     * @return 设备集合
     */
    public List<SerialConfig> selectSerialConfigList(SerialConfig serialConfig) {
        return serialConfigMapper.selectSerialConfigList(serialConfig);
    }

    /**
     * 查询单个串口名称配置
     * @param serialConfig
     * @return
     */
    public SerialConfig selectSerialConfig(SerialConfig serialConfig){
        return serialConfigMapper.selectSerialConfig(serialConfig);
    }

    /**
     * 新增串口名称配置
     *
     * @param serialConfig
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addSerialConfig(SerialConfig serialConfig) {
        return serialConfigMapper.addSerialConfig(serialConfig);
    }

    /**
     * 修改串口名称配置
     *
     * @param serialConfig
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateSerialConfig(SerialConfig serialConfig) {
        return serialConfigMapper.updateSerialConfig(serialConfig);
    }

    /**
     * 批量删除串口名称配置
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    public int deleteSerialConfigByIds(Long[] ids) {
        return serialConfigMapper.deleteSerialConfigByIds(ids);
    }

}
