package com.xhjt.project.device.service;

import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.ConnectLog;
import com.xhjt.project.device.mapper.ConnectLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ConnectLogService {

    @Autowired
    private ConnectLogMapper connectLogMapper;


    /**
     * 查询设备列表
     *
     * @param connectLog
     * @return 设备集合
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ConnectLog> selectConnectLogList(ConnectLog connectLog) {
        return connectLogMapper.selectConnectLogList(connectLog);
    }

    /**
     * 新增设备
     *
     * @param am
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addConnectLog(AcquisitionMiddleware am,Integer action) {
        ConnectLog connectLog = new ConnectLog();

        connectLog.setIp(am.getIp());
        connectLog.setPort(am.getPort());
        connectLog.setName(am.getName());
        connectLog.setType(am.getType());
        connectLog.setMac(am.getMac());
        connectLog.setAction(action);

        return connectLogMapper.addConnectLog(connectLog);
    }


}
