package com.xhjt.project.device.mapper;



import com.xhjt.dctcore.commoncore.domain.device.DeviceAttributeEntity;

import java.util.List;

/**
 * 设备属性管理 数据层
 *
 * <AUTHOR>
 */
public interface DeviceAttributeMapper {
    /**
     * 查询设备信息
     *
     * @param deviceAttribute 设备信息
     * @return 设备信息
     */
    public DeviceAttributeEntity selectDeviceAttribute(DeviceAttributeEntity deviceAttribute);

    /**
     * 查询设备列表
     *
     * @param deviceAttribute 设备信息
     * @return 设备集合
     */
    public List<DeviceAttributeEntity> selectDeviceAttributeList(DeviceAttributeEntity deviceAttribute);

    /**
     * 新增设备
     *
     * @param deviceAttribute 设备信息
     * @return 结果
     */
    public int addDeviceAttribute(DeviceAttributeEntity deviceAttribute);

    /**
     * 修改设备
     *
     * @param deviceAttribute 设备信息
     * @return 结果
     */
    public int updateDeviceAttribute(DeviceAttributeEntity deviceAttribute);

    /**
     * 删除设备
     *
     * @param deviceAttributeId 参数ID
     * @return 结果
     */
    public int deleteDeviceAttributeById(Long deviceAttributeId);

    /**
     * 批量删除参数信息
     *
     * @param deviceAttributeIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteDeviceAttributeByIds(Long[] deviceAttributeIds);

}
