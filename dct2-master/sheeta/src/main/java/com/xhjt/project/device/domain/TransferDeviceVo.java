package com.xhjt.project.device.domain;

import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;

import java.util.List;

/**
 * Created by chenmingyong on 2021/10/23.
 */
public class TransferDeviceVo {
    private DeviceEntity deviceEntity;
    private List<TransferAttributeEntity> transferAttributeEntities;

    public DeviceEntity getDeviceEntity() {
        return deviceEntity;
    }

    public void setDeviceEntity(DeviceEntity deviceEntity) {
        this.deviceEntity = deviceEntity;
    }

    public List<TransferAttributeEntity> getTransferAttributeEntities() {
        return transferAttributeEntities;
    }

    public void setTransferAttributeEntities(List<TransferAttributeEntity> transferAttributeEntities) {
        this.transferAttributeEntities = transferAttributeEntities;
    }
}
