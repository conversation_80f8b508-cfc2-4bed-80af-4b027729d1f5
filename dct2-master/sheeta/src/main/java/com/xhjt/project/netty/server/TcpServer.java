package com.xhjt.project.netty.server;

import com.xhjt.project.netty.coder.SmartTypeFrameDecoder;
import com.xhjt.project.netty.handler.ChannelManagerHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 创建服务端
 *
 * <AUTHOR>
 */
public class TcpServer {
    protected Logger logger = LoggerFactory.getLogger(TcpServer.class);

    private static Map<String, TcpServer> map = new ConcurrentHashMap<>();
    private ServerBootstrap serverBootstrap;
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;

    public TcpServer() {
        this.bossGroup = new NioEventLoopGroup(1);
        this.workerGroup = new NioEventLoopGroup(2);
        this.serverBootstrap = new ServerBootstrap();
        this.serverBootstrap.group(this.bossGroup, this.workerGroup)
                .channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, 128)
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    // 给pipeline设置处理器
                    @Override
                    protected void initChannel(SocketChannel socketChannel) throws Exception {
                        socketChannel.pipeline().addLast(new SmartTypeFrameDecoder(1024));
                        socketChannel.pipeline().addLast(new ChannelManagerHandler());
                    }
                });
    }

    public static TcpServer getTcpServer() {
        String key = "TCP_SERVER_";
        // 判断是否已经创建了netty线程池
        if (map.get(key) != null) {
            return map.get(key);
        }

        TcpServer tcpServer = new TcpServer();
        map.put(key, tcpServer);

        return tcpServer;
    }

    public void bind(int port) {
        this.serverBootstrap.bind(port).addListener(new ChannelFutureListener() {
            @Override
            public void operationComplete(ChannelFuture future) throws Exception {
                if (future.isSuccess()) {
                    logger.info("服务创建成功-----{}", port);
                } else {
                    logger.info("服务创建失败-----{}", port);
                    //重新启动
                }
            }
        });
    }

    private void close() {
        if (this.bossGroup != null) {
            this.bossGroup.shutdownGracefully();
        }
        if (this.workerGroup != null) {
            this.workerGroup.shutdownGracefully();
        }
    }

    public static void cleanTcpServerMap() {
        if (map == null || map.size() == 0) {
            return;
        }
        for (String key : map.keySet()) {
            map.get(key).close();
        }
        map.clear();
    }

}
