package com.xhjt.project.snapshot.service;

import com.xhjt.common.utils.ZipMultiFileUtil;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.framework.config.ProjectConfig;
import com.xhjt.project.snapshot.mapper.SnapshotLogMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 快照截图记录 实现类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotLogService {
    public final static Logger logger = LoggerFactory.getLogger(SnapshotLogService.class);
    @Value("${project.snapshotPath}")
    private String snapshotPath;


    @Autowired
    private SnapshotLogMapper snapshotLogMapper;


    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotLogEntity> selectSnapshotLogList(SnapshotLogEntity snapshotLog) {
        return snapshotLogMapper.selectSnapshotLogList(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotLogEntity selectSnapshotLogById(Long id) {
        SnapshotLogEntity snapshotLog = new SnapshotLogEntity();
        snapshotLog.setId(id);
        return snapshotLogMapper.selectSnapshotLog(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotLogEntity> selectSnapshotLogByChannel(SnapshotLogEntity snapshotLog) {
        return snapshotLogMapper.selectSnapshotLogList(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotLogEntity> selectSnapshotLogByChannelCode(String channelCode) {
        SnapshotLogEntity snapshotLog = new SnapshotLogEntity();
        snapshotLog.setChannelCode(channelCode);
        return snapshotLogMapper.selectSnapshotLogList(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public int addSnapshotLog(SnapshotLogEntity snapshotLog) {
        return snapshotLogMapper.addSnapshotLog(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotLogById(Long id) {
        return snapshotLogMapper.deleteSnapshotLogById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int addLogByHandler(String channelCode, Long time, String directory) {
        SnapshotLogEntity snapshotLog = new SnapshotLogEntity();

        snapshotLog.setChannelCode(channelCode);
        snapshotLog.setOperateTime(time);
        snapshotLog.setFileName(time + ".jpg");
        snapshotLog.setDirectory(directory);
        return addSnapshotLog(snapshotLog);
    }

    /**
     * 根据条件查找到所有图片并压缩输出
     */
    public String zipwordDownAction(List<SnapshotLogEntity> listOld){
        List<SnapshotLogEntity> list= listOld.stream().filter(distinctByKey(SnapshotLogEntity::getFileName)).collect(Collectors.toList());
        String dirPath = ProjectConfig.getProfile();
        File dir = new File(dirPath);
        String filePath = dirPath + "/img" + System.currentTimeMillis() + ".zip";
        File zipFile = new File(filePath);
        try {
            if (!dir.exists()){
                dir.mkdirs();
            }
            if (!zipFile.exists()) {
                zipFile.createNewFile();
            }else {
                zipFile.delete();
                zipFile.createNewFile();
            }

        } catch (Exception e) {
            logger.error("写入文件失败");
        }
        List<File> srcFiles = new ArrayList<File>();
        for (int i = 0; i < list.size(); i++) {
            String fileName = list.get(i).getDirectory()+"/"+list.get(i).getFileName();
            srcFiles.add(new File(fileName));
        }

        // 调用压缩方法
        ZipMultiFileUtil.zipFiles(srcFiles, zipFile);
        return zipFile.getName();
    }

    /**
     * 去重重写方法
     * @param keyExtractor
     * @param <T>
     * @return
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }
    /**
     * 通过时间删除
     * @param timeInMillis
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteSnapshotLogByTime(long timeInMillis) {
        //传过来的是距离现在90天的时间戳
        snapshotLogMapper.deleteSnapshotLogByTime(timeInMillis);
    }
}
