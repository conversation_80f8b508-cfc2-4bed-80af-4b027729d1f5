package com.xhjt.project.device.controller;

import com.xhjt.common.ClientListernerUtil;
import com.xhjt.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * websocket服务
 */
@ServerEndpoint(value = "/wsServer/{sid}/{mac}/{wsType}/{type}")
@Component
public class WebSocketServer {

    private static Logger logger = LoggerFactory.getLogger(WebSocketServer.class);
    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static int onlineCount = 0;

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
     */
    private static CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<WebSocketServer>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 接收sid
     */
    private String sid = "";

    /**
     * 设备MAC
     */
    private String mac;

    /**
     * 0预览 1解析预览
     */
    private Integer wsType;

    /**
     * 模板类型
     */
    private Integer type;


    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "sid") String sid, @PathParam(value = "mac") String mac, @PathParam(value = "wsType") Integer wsType, @PathParam(value = "type") Integer type) {
        this.session = session;
        //加入set中
        webSocketSet.add(this);
        //在线数加1
        addOnlineCount();
        logger.info("websocket有新窗口开始监听:" + sid + ",当前在线人数为" + getOnlineCount());
        this.sid = sid;
        this.mac = mac;
        this.wsType = wsType;
        if (wsType == 1){
            ClientListernerUtil.setWsFlag(false);
            ClientListernerUtil.setCloseFlag(true);
        }
        ClientListernerUtil.setWsState(wsType);
        ClientListernerUtil.setMac(mac);
        ClientListernerUtil.setType(type);
        logger.info("websocket有新窗口开始监听:" + ClientListernerUtil.type);
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        //从set中删除
        webSocketSet.remove(this);
        //在线数减1
        subOnlineCount();
        logger.info("websocket有一连接关闭！当前在线人数为" + getOnlineCount());
        if (this.wsType == 1){
            ClientListernerUtil.setWsFlag(true);
            ClientListernerUtil.setCloseFlag(false);
            ClientListernerUtil.setWsState(0);
        }
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        logger.info("收到来自窗口" + sid + "的信息:" + message);
        //群发消息
        for (WebSocketServer item : webSocketSet) {
            item.sendMessage(message);
        }
    }

    /**
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        logger.error("发生错误");
        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送
     */
    private void sendMessage(String message) {
        try {
            this.session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            logger.error("websocket发送出错，---{}", e);
        }
    }

    /**
     * 群发自定义消息
     */
    private static void sendInfo(String message, @PathParam("sid") String sid) {
        webSocketSet.stream().filter(ws -> ws.sid.equals(sid)).forEach(ws -> ws.sendMessage(message));
    }

    /**
     * 发送实时数据
     *
     * @param message
     * @param mac
     */
    public static void sendRealTimeData(String message, String mac) {
        if (StringUtils.isBlank(mac)) {
            return;
        }
        List<String> macList = getAllWebsocketMac();
        if (macList == null || !macList.contains(mac)) {
            return;
        }

        webSocketSet.stream().filter(ws -> ws.mac.equals(mac)).forEach(ws -> sendInfo(message, ws.sid));
    }

    private static List<String> getAllWebsocketMac() {
        if (webSocketSet.size() == 0) {
            return null;
        }
        return webSocketSet.stream().map(ws -> ws.mac).collect(Collectors.toList());
    }

    private static synchronized int getOnlineCount() {
        return onlineCount;
    }

    private static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount++;
    }

    private static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount--;
    }
}
