package com.xhjt.framework.serialport;

import com.xhjt.common.ClientListernerUtil;
import com.xhjt.common.SerialPortUtil;
import com.xhjt.common.constant.Constants;
import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.framework.redis.RedisCache;
import com.xhjt.project.device.controller.WebSocketServer;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.SerialConfig;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.SerialConfigService;
import com.xhjt.project.netty.service.StoreService;
import gnu.io.SerialPort;
import gnu.io.SerialPortEvent;
import gnu.io.SerialPortEventListener;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 串口
 *
 * <AUTHOR>
 */
@Service
public class RtxtService {
    private static final Logger logger = LoggerFactory.getLogger(RtxtService.class);

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private StoreService storeService;

    @Autowired
    public ClientListernerUtil clientListernerUtil;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SerialConfigService serialConfigService;
    public void handleAllService() {
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setConnectType(1);//串口
        deviceEntity.setEnable(1);
        List<DeviceEntity> list = deviceService.selectDeviceList(deviceEntity);
        for (DeviceEntity device: list) {
            toListenerSerialPort(device);
        }

    }


    public Integer toListenerSerialPort(DeviceEntity deviceEntity) {
        String oldSerialPort = getOldSerialPort(deviceEntity.getSerialPort());
        if (StringUtils.isEmpty(oldSerialPort)){
            //如果没找到对应得老串口号，则不开启串口,直接退出
            return 0;
        }
        SerialPort serialPort = SerialPortUtil.openComPort(oldSerialPort, deviceEntity.getBaudRate(), deviceEntity.getDataBits(), deviceEntity.getStopBits(), deviceEntity.getParity());
        if (serialPort == null) {
            return 0;
        }
        SerialPortUtil.addOpenPort(deviceEntity.getSerialPort(), serialPort);

        // 设置串口监听
        SerialPortUtil.setListenerToSerialPort(serialPort, new SerialPortEventListener() {
            @Override
            public void serialEvent(SerialPortEvent arg0) {
                //数据通知
                if (arg0.getEventType() == SerialPortEvent.DATA_AVAILABLE) {
                    byte[] bytes = SerialPortUtil.readData(serialPort, 20);
                    //这边写入redis,用来后续判断连接是否已经断开
                    redisCache.setCacheObject("CJ_CK_"+deviceEntity.getSerialPort(),"1",5, TimeUnit.MINUTES);
                    Long time = System.currentTimeMillis();
                    //一分钟收集采集数据逻辑实现
                    clientListernerUtil.redisCompare(bytes);
                    // websocket发送实时数据
                    if (ClientListernerUtil.states == 0) {
                        WebSocketServer.sendRealTimeData(new String(bytes, StandardCharsets.UTF_8), deviceEntity.getSerialPort());
                    }
                    //新增的时候-解析预览接口
                    if (ClientListernerUtil.states == 1) {
                        KafkaMessage kafkaMessage = getReceiveMessage(deviceEntity,bytes,time);
                        clientListernerUtil.parseSerilView(kafkaMessage, deviceEntity.getSerialPort());
                    }
                    logger.info("串口监听1111--名称：{}-- 数据:{}", deviceEntity.getSerialPort(), new String(bytes));
                    // 禁用状态，不接收数据
                    if (deviceEntity.getEnable() == 0) {
                        return;
                    }
                    //存储状态或者传输状态下进行往下走
                    if (deviceEntity.getEnable()==1 || deviceEntity.getTransferStatus()==1){
                        KafkaMessage kafkaMessage = getReceiveMessage(deviceEntity,bytes,time);
                        //发送数据到kafka
                        storeService.send2Kafka(kafkaMessage);

                        //存入txt文件
                        storeService.save2Txt(kafkaMessage);
                    }
                }
            }
        });
        return 1;
    }
    private static String bytesToTenString(byte[] byteArr) {
        StringBuilder sb = new StringBuilder(byteArr.length);
        String sTemp;
        for (byte b : byteArr) {
            sTemp = Integer.toHexString(0xFF & b);
            long decNum = Long.parseLong(sTemp, 16);
            sb.append(decNum).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 获取旧串口名称
     * @param newSerialPort
     * @return
     */
    public static String getOldSerialPort(String newSerialPort){
        SerialConfigService serialConfigService = SpringUtils.getBean(SerialConfigService.class);
        String oldSerialName = "";
        if (StringUtils.isNotBlank(newSerialPort)){
            SerialConfig se = new SerialConfig();
            se.setNewName(newSerialPort);
            SerialConfig serialConfig = serialConfigService.selectSerialConfig(se);
            if (serialConfig != null){
                oldSerialName = serialConfig.getOldName();
            }else {
                return null;
            }
        }else {
            return null;
        }
        return oldSerialName;
    }

    /**
     * 原始数据多余字符处理
     * @param deviceEntity
     * @param bytes
     * @param time
     * @return
     */
    public static KafkaMessage getReceiveMessage(DeviceEntity deviceEntity,byte[] bytes,Long time){
        String receiveMessage;
        if (DeviceTypeEnum.ATTITUDE.getValue().equals(deviceEntity.getType())) {
            // ATTITUDE
            receiveMessage = bytesToTenString(bytes);
        } else {
            receiveMessage = new String(bytes, StandardCharsets.UTF_8);
            if (receiveMessage.contains("@@")) {
                receiveMessage = receiveMessage.substring(8);
            }
        }
        if (receiveMessage.contains("\n\t")){
            receiveMessage = receiveMessage.replace("\n","");
            //删除\t
            receiveMessage = receiveMessage.replace("\t","");
        }
        KafkaMessage kafkaMessage = new KafkaMessage(deviceEntity.getType(), deviceEntity.getCode(), receiveMessage, deviceEntity.getCost(), time);
        return kafkaMessage;
    }
}
