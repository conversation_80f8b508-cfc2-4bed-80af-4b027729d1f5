package com.xhjt.framework.init;

import com.xhjt.common.SerialPortUtil;
import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.framework.serialport.RtxtService;
import com.xhjt.project.device.service.AcquisitionMiddlewareService;
import com.xhjt.project.netty.common.AmUtil;
import com.xhjt.project.netty.common.DeviceUtil;
import com.xhjt.project.netty.common.RedisDeviceUtil;
import com.xhjt.project.netty.server.TcpServer;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * class
 *
 * <AUTHOR>
 * @date 2020/3/5 23:55
 */
@Component
@Order(0)
public class SystemLoad implements CommandLineRunner {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void run(String... args) throws Exception {
        logger.info("系统基础功能初始化开始...");


        AcquisitionMiddlewareService acquisitionMiddlewareService = SpringUtils.getBean(AcquisitionMiddlewareService.class);
        acquisitionMiddlewareService.connectStatusInvalid();
        acquisitionMiddlewareService.delete4Invalid();
        //串口
        SerialPortUtil.findSystemAllSerialPort();
        SerialPortUtil.cleanOpenMap();

        AmUtil.cleanMap();

        // 缓存所有的设备
        logger.info("缓存所有的设备...");
        DeviceUtil.addAllDevice();
        RedisDeviceUtil.addAllDevice();
        // 缓存所有的快照信息
        SnapshotUtil.addAllInfo();

        TcpServer.cleanTcpServerMap();
        TcpServer tcpServer = TcpServer.getTcpServer();
        tcpServer.bind(22333);
        //串口监听
        RtxtService inputService = SpringUtils.getBean(RtxtService.class);
        inputService.handleAllService();
        logger.info("系统基础功能初始化结束...");
    }
}
