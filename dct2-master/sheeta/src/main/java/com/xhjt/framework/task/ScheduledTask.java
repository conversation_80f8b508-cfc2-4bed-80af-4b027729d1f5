package com.xhjt.framework.task;

import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.framework.redis.RedisCache;
import com.xhjt.project.collect.service.CollectModuleService;
import com.xhjt.project.collect.service.CollectNodeService;
import com.xhjt.project.device.domain.*;
import com.xhjt.project.device.service.AcquisitionMiddlewareService;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.TransferAttributeService;
import com.xhjt.project.index.domain.ShipTerminalManage;
import com.xhjt.project.index.service.IndexService;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotHandlerService;
import com.xhjt.project.snapshot.service.SnapshotTransferService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("scheduledTask")
public class ScheduledTask {

    private Logger logger = LoggerFactory.getLogger(ScheduledTask.class);

    @Autowired
    private SnapshotHandlerService snapshotHandlerService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private TransferAttributeService transferAttributeService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private AcquisitionMiddlewareService amService;
    @Autowired
    private SnapshotChannelService snapshotChannelService;
    @Autowired
    private SnapshotTransferService snapshotTransferService;
    @Autowired
    private CollectNodeService collectNodeService;
    @Autowired
    private CollectModuleService collectModuleService;
    @Autowired
    private IndexService indexService;

    public void multipleParams(String s, Boolean b, Long l, Double d, Integer i) {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void haveParams(String params) {
        System.out.println("执行有参方法：" + params);
    }

    public void noParams() {
        System.out.println("执行无参方法");
    }

    /**
     * 获取截屏图片
     */
    public void obtainScreenShot(String channelCode){
        logger.info("获取摄像头截屏。。。。{}", System.currentTimeMillis());
        try{
            snapshotHandlerService.start(channelCode);
        }catch (Throwable t){
            logger.error("获取摄像头截屏异常。。。。{}", t);
        }

    }

    /**
     * 定时同步设备、快照信息到岸端
     */
    public void sys2Msg() {
        boolean flagChannel = false;
        boolean flagDevice = false;
        SyncAllEntity syncAllEntity = new SyncAllEntity();
        List<ChannelStatus> channelStatuses = new ArrayList<>();
        List<DeviceStatus> deviceStatuses = new ArrayList<>();
        //设备同步到岸端
        List<DeviceEntity> list = deviceService.selectDeviceList(null);
        if (list != null && list.size() > 0) {
            flagDevice = true;
            for (DeviceEntity deviceEntity : list) {
                if (deviceEntity.getConnectType() == 1) {
                    //串口-定时那边根据redis上得数据判断是否有则为串口得连接状态修改设备连接状态
                    String data = redisCache.getCacheObject("CJ_CK_" + deviceEntity.getSerialPort());
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(data)) {
                        //有数据则说明5分钟内是正常连接得-修改入库
                        deviceEntity.setConnectStatus(1);
                    } else {
                        deviceEntity.setConnectStatus(0);
                    }
                } else {
                    //采集终端-定时那边根据采集终端当前得状态来修正设备得连接状态
                    AcquisitionMiddleware am = amService.selectAcquisitionMiddlewareByMac(deviceEntity.getMac());
                    if (am != null) {
                        deviceEntity.setConnectStatus(am.getConnectStatus());
                    }
                }
                int row = deviceService.updateDevice(deviceEntity);
                if (row > 0) {
                    DeviceStatus deviceStatus = new DeviceStatus();
                    deviceStatus.setCode(deviceEntity.getCode());
                    deviceStatus.setTransferStatus(deviceEntity.getTransferStatus());
                    deviceStatus.setConnectStatus(deviceEntity.getConnectStatus());
                    deviceStatus.setEnable(deviceEntity.getEnable());
                    deviceStatuses.add(deviceStatus);
                }
            }

        }

        //获取快照信息同步到岸端
        List<SnapshotChannelEntity> channelList = snapshotChannelService.selectSnapshotChannelList(null);
        if (channelList != null && channelList.size() > 0) {
            flagChannel = true;
            for (SnapshotChannelEntity snapshotChannelEntity : channelList) {
                Integer status = 0;
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateS1 = df.format(new Date());
                Long operateTime = null;
                String dateS2 = "";
                if (snapshotChannelEntity.getOperateTime() != null) {
                    operateTime = snapshotChannelEntity.getOperateTime();
                    dateS2 = DateUtils.parseTimeToDate(operateTime, "yyyy-MM-dd HH:mm:ss");
                }
                try {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(dateS2)) {
                        Date date1 = df.parse(dateS1);
                        Date date2 = df.parse(dateS2);
                        long diff = date1.getTime() - date2.getTime();
                        long seconds = diff / 1000;
                        logger.info(seconds + "seconds");
                        if (seconds - (snapshotChannelEntity.getCompartment()+60) > 0) {
                            status = 0;
                        } else {
                            status = 1;
                        }
                    }

                    if (snapshotChannelEntity.getCompartment() == null || org.apache.commons.lang3.StringUtils.isBlank(dateS2)) {
                        status = 0;
                    }

                } catch (ParseException e) {
                    status = 0;
                    logger.error("日期转义失败");
                }
                ChannelStatus channelStatus = new ChannelStatus();
                channelStatus.setTrStatus(snapshotChannelEntity.getTrStatus());
                channelStatus.setTransferStatus(snapshotChannelEntity.getTransferStatus());
                channelStatus.setCode(snapshotChannelEntity.getCode());
                channelStatus.setStatus(status);
                channelStatuses.add(channelStatus);
            }
        }
        if (flagChannel == false && flagDevice == false){
            //设备跟快照都为空得时候直接不做定时发送
        }else {
            syncAllEntity.setDeviceStatuses(deviceStatuses);
            syncAllEntity.setChannelStatuses(channelStatuses);
            amService.syncAllNewShore(syncAllEntity, 2);
        }

    }

    /**
     * 定时同步船舶终端管理信息
     */
    public void ship2Msg() {
//        try {
//            ShipTerminalManage shipTerminalManage = indexService.getShipTerminalManage();
//            amService.syncManage2Shore(shipTerminalManage,2);
//        } catch (IOException e) {
//            logger.error("定时同步船舶终端管理信息出错---{}",e);
//        }
    }
}
