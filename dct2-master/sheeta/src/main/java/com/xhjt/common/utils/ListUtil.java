package com.xhjt.common.utils;

import com.google.common.collect.Lists;
import com.xhjt.common.exception.BaseException;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

/**
 * <b>功能描述:</b>
 * <pre>
 * List集合工具类
 */
public class ListUtil {


    /**
     * 从bean集合中获取ID值的集合
     *
     * @param list         bean对象集合
     * @param keyFieldName id字段名称
     * @param <T>
     * @return
     */
    public static <T> List<Long> fetchIdList(List<T> list, String keyFieldName) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Long> result = new ArrayList<Long>();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        String fieldValue = null;
        for (T obj : list) {
            fieldValue = BeanUtils.getProperty(obj, keyFieldName);

            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }

            result.add(Long.valueOf(fieldValue));
        }

        return result;
    }

    /**
     * 从bean集合中获取字段String值的集合
     *
     * @param list         bean对象集合
     * @param keyFieldName id字段名称
     * @param <T>
     * @return
     */
    public static <T> List<String> fetchFieldValueList(List<T> list, String keyFieldName) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<String> result = new ArrayList<String>();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        String fieldValue = null;
        for (T obj : list) {
            fieldValue = BeanUtils.getProperty(obj, keyFieldName);

            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }

            result.add(String.valueOf(fieldValue));
        }

        return result;
    }

    /**
     * 从bean集合中获取字段Interger值的集合
     *
     * @param list         bean对象集合
     * @param keyFieldName id字段名称
     * @param <T>
     * @return
     */
    public static <T> List<Integer> fetchIntegerFieldValueList(List<T> list, String keyFieldName) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Integer> result = new ArrayList<Integer>();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        String fieldValue = null;
        for (T obj : list) {
            fieldValue = BeanUtils.getProperty(obj, keyFieldName);

            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }

            result.add(Integer.valueOf(fieldValue));
        }

        return result;
    }

    public static <T> List<Long> fetchNotEmptyIdList(List<T> list, String keyFieldName) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Long> resultList = fetchIdList(list,keyFieldName);
        if(CollectionUtils.isEmpty(resultList)){
            return Lists.newArrayList(-1L);
        }
        return resultList;
    }

    /**
     *从集合中获取符合特定字段，特定整型值的bean，返回新集合
     *
     * @param list 集合
     * @param fieldName 字段名称
     * @param val 字段值
     * @param <T>
     * @return
     * @throws IllegalAccessException
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     */
    public static <T> List getBeanListByLongValue(List<T> list, String fieldName, Long val) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<T> newList = new ArrayList<>();
        Long fieldValue;
        for (T t : list) {
            fieldValue = Long.parseLong(BeanUtils.getProperty(t,fieldName));
            if (val.equals(fieldValue)) {
                newList.add(t);
            }
        }
        return newList;
    }

    public static <T> T getBeanByIntegerValue(List<T> list, String fieldName, Integer val) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<T> newList = new ArrayList<>();
        Integer fieldValue;
        for (T t : list) {
            fieldValue = Integer.parseInt(BeanUtils.getProperty(t,fieldName));
            if (val.equals(fieldValue)) {
                newList.add(t);
            }
        }

        if(newList.size() > 0){
            return newList.get(0);
        } else {
            return null;
        }
    }

    /**
     *从集合中删除符合特定字段，特定整型值的bean，返回新集合
     *
     * @param list 集合
     * @param fieldName 字段名称
     * @param val 字段值
     * @param <T>
     * @return
     * @throws IllegalAccessException
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     */
    public static <T> List removeBeanByLongValue(List<T> list, String fieldName, Long val) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<T> newList = new ArrayList<>();
        Long fieldValue;
        for (T t : list) {
            fieldValue = Long.parseLong(BeanUtils.getProperty(t,fieldName));
            if (!val.equals(fieldValue)) {
                newList.add(t);
            }
        }
        return newList;
    }

    /**
     *从集合中删除符合特定字段，特定整型值的bean，返回新集合
     *
     * @param list 集合
     * @param fieldName 字段名称
     * @param val 字段值
     * @param <T>
     * @return
     * @throws IllegalAccessException
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     */
    public static <T> List removeBeanByIntegerValue(List<T> list, String fieldName, Integer val) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<T> newList = new ArrayList<>();
        Integer fieldValue;
        for (T t : list) {
            fieldValue = Integer.parseInt(BeanUtils.getProperty(t,fieldName));
            if (!val.equals(fieldValue)) {
                newList.add(t);
            }
        }
        return newList;
    }

    public static <T> List<String> fetchFieldValuesList(List<T> list, String... keyFieldName ) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<String> result = new ArrayList<String>();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        String fieldValue = null;
        for (T obj : list) {
            for (int i = 0; i < keyFieldName.length; i++) {
                fieldValue = BeanUtils.getProperty(obj, keyFieldName[i]);

                if (StringUtils.isBlank(fieldValue)) {
                    continue;
                }

                result.add(String.valueOf(fieldValue));
            }
        }

        return result;
    }

    public static List<Long> conversionIdStr2List(String ids) throws BaseException{
        if(StringUtils.isBlank(ids)){
            throw new BaseException("id不能为空！");
        }

        String[] idStr = ids.split(",");
        List<Long> idList = new ArrayList<>();

        for (int i = 0; i < idStr.length; i++) {
            idList.add(Long.parseLong(idStr[i]));
        }

        return idList;
    }

    public static List<String> conversionStr2List(String str,String separator) throws BaseException{
        if(StringUtils.isBlank(str)){
            throw new BaseException("字符串不能为空！");
        }

        String[] strArray = str.split(separator);
        List<String> list = new ArrayList<>();

        for (int i = 0; i < strArray.length; i++) {
            list.add(strArray[i]);
        }

        return list;
    }

    /**
     * 克隆list
     * @param list
     * @param <T>
     * @return
     * @throws InvocationTargetException
     * @throws NoSuchMethodException
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    public static <T> List<T> clone(List<T> list) throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        List<T> newList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return newList;
        }
        for (T t : list) {
            newList.add((T) BeanUtils.cloneBean(t));
        }
        return newList;
    }
}
