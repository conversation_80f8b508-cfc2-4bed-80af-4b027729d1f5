package com.xhjt.common;

public class RedisParameter {
    /**
     * 机舱 ftp ip地址
     */
    public static String SHIP_ENGINE_ROOM_FTP_IP = "SHIP_ENGINE_ROOM_FTP_IP";

    /**
     * 机舱 ftp 端口
     */
    public static String SHIP_ENGINE_ROOM_FTP_PORT = "SHIP_ENGINE_ROOM_FTP_PORT";

    /**
     * 机舱 ftp 用户名
     */
    public static String SHIP_ENGINE_ROOM_FTP_USER = "SHIP_ENGINE_ROOM_FTP_USER";

    /**
     * 机舱 ftp 密码
     */
    public static String SHIP_ENGINE_ROOM_FTP_PASSWORD = "SHIP_ENGINE_ROOM_FTP_PASSWORD";

    /**
     * 保存截屏时间
     */
    public static String LATEST_PICTURE_DATE = "LATEST_PICTURE_DATE_";

    public static String SHIP_SN = "SHIP_SN_";
}
