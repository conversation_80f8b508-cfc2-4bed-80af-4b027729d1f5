
-- ----------------------------
-- 1、初始化-部门表数据
-- ----------------------------
insert into sys_dept values(100,  0,   '0', 'XX公司',   0, 'XX', '15888888888', '<EMAIL>', '0', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00');

-- ----------------------------
-- 2、初始化-用户信息表数据
-- ----------------------------
insert into sys_user values(1,  100, 'master009', '管理员009', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$DkCWiS72nwGplbFgp/JriuO8r8oE9CkYCwPcQcNQz6My0UwnAcaU2', '0', '0', '127.0.0.1', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '管理员');
insert into sys_user values(2,  100, 'admin', '管理员', '00', '<EMAIL>', '15888888887', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '管理员');

-- ----------------------------
-- 3、初始化-岗位信息表数据
-- ----------------------------
insert into sys_post values(1, 'ceo',  '董事长',    1, '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_post values(2, 'se',   '项目经理',  2, '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_post values(3, 'hr',   '人力资源',  3, '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_post values(4, 'user', '普通员工',  4, '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');

-- ----------------------------
-- 4、初始化-角色信息表数据
-- ----------------------------
insert into sys_role values('1', '超级管理员',   'admin',  1, 1, '0', '0', 'system', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '超级管理员');
insert into sys_role values('2', '管理员',   'manage',  1, 1, '0', '0', 'system', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '管理员');

-- ----------------------------
-- 13、初始化-用户和角色关联表数据
-- ----------------------------
insert into sys_user_role values ('1', '1');
insert into sys_user_role values ('2', '2');

-- ----------------------------
-- 5、初始化-菜单信息表数据
-- ----------------------------
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '系统管理', 0, 10, 'system', NULL, 1, 'M', '0', '', 'system', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '系统管理目录');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '系统监控', 0, 11, 'monitor', NULL, 1, 'M', '0', '', 'monitor', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '系统监控目录');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', 1, 'C', '0', 'system:user:list', 'user', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '用户管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', 1, 'C', '0', 'system:role:list', 'peoples', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '角色管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', 1, 'C', '0', 'system:menu:list', 'tree-table', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '菜单管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', 1, 'C', '0', 'system:dept:list', 'tree', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '部门管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', 1, 'C', '0', 'system:post:list', 'post', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '岗位管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', 1, 'C', '0', 'system:dict:list', 'dict', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '字典管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', 1, 'C', '0', 'system:config:list', 'edit', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '参数设置菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', 1, 'C', '0', 'system:notice:list', 'message', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '通知公告菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (108, '日志管理', 2, 5, 'log', 'system/log/index', 1, 'M', '0', '', 'log', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '日志管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', 1, 'C', '0', 'monitor:online:list', 'online', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '在线用户菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', 1, 'C', '0', 'monitor:job:list', 'job', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '定时任务菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', 1, 'C', '0', 'monitor:druid:list', 'druid', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '数据监控菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (112, '串口名称配置', 1, 9, 'serial', 'system/serial/index', 1, 'C', '0', 'system:serial:list', 'table', 'admin', '2021-10-12 17:33:00', 'master009', '2021-10-26 18:06:33', '系统配置时间菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', 1, 'C', '0', 'monitor:operlog:list', 'form', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '操作日志菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', 1, 'C', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '登录日志菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1001, '用户查询', 100, 1, '', '', 1, 'F', '0', 'system:user:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1002, '用户新增', 100, 2, '', '', 1, 'F', '0', 'system:user:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1003, '用户修改', 100, 3, '', '', 1, 'F', '0', 'system:user:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1004, '用户删除', 100, 4, '', '', 1, 'F', '0', 'system:user:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1005, '用户导出', 100, 5, '', '', 1, 'F', '0', 'system:user:export', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1006, '用户导入', 100, 6, '', '', 1, 'F', '0', 'system:user:import', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1007, '重置密码', 100, 7, '', '', 1, 'F', '0', 'system:user:resetPwd', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1008, '角色查询', 101, 1, '', '', 1, 'F', '0', 'system:role:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1009, '角色新增', 101, 2, '', '', 1, 'F', '0', 'system:role:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1010, '角色修改', 101, 3, '', '', 1, 'F', '0', 'system:role:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1011, '角色删除', 101, 4, '', '', 1, 'F', '0', 'system:role:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1012, '角色导出', 101, 5, '', '', 1, 'F', '0', 'system:role:export', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1013, '菜单查询', 102, 1, '', '', 1, 'F', '0', 'system:menu:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1014, '菜单新增', 102, 2, '', '', 1, 'F', '0', 'system:menu:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1015, '菜单修改', 102, 3, '', '', 1, 'F', '0', 'system:menu:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1016, '菜单删除', 102, 4, '', '', 1, 'F', '0', 'system:menu:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1017, '部门查询', 103, 1, '', '', 1, 'F', '0', 'system:dept:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1018, '部门新增', 103, 2, '', '', 1, 'F', '0', 'system:dept:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1019, '部门修改', 103, 3, '', '', 1, 'F', '0', 'system:dept:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1020, '部门删除', 103, 4, '', '', 1, 'F', '0', 'system:dept:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1021, '岗位查询', 104, 1, '', '', 1, 'F', '0', 'system:post:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1022, '岗位新增', 104, 2, '', '', 1, 'F', '0', 'system:post:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1023, '岗位修改', 104, 3, '', '', 1, 'F', '0', 'system:post:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1024, '岗位删除', 104, 4, '', '', 1, 'F', '0', 'system:post:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1025, '岗位导出', 104, 5, '', '', 1, 'F', '0', 'system:post:export', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1026, '字典查询', 105, 1, '#', '', 1, 'F', '0', 'system:dict:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1027, '字典新增', 105, 2, '#', '', 1, 'F', '0', 'system:dict:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1028, '字典修改', 105, 3, '#', '', 1, 'F', '0', 'system:dict:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1029, '字典删除', 105, 4, '#', '', 1, 'F', '0', 'system:dict:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1030, '字典导出', 105, 5, '#', '', 1, 'F', '0', 'system:dict:export', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1031, '参数查询', 106, 1, '#', '', 1, 'F', '0', 'system:config:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1032, '参数新增', 106, 2, '#', '', 1, 'F', '0', 'system:config:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1033, '参数修改', 106, 3, '#', '', 1, 'F', '0', 'system:config:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1034, '参数删除', 106, 4, '#', '', 1, 'F', '0', 'system:config:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1035, '参数导出', 106, 5, '#', '', 1, 'F', '0', 'system:config:export', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1036, '公告查询', 107, 1, '#', '', 1, 'F', '0', 'system:notice:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1037, '公告新增', 107, 2, '#', '', 1, 'F', '0', 'system:notice:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1038, '公告修改', 107, 3, '#', '', 1, 'F', '0', 'system:notice:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1039, '公告删除', 107, 4, '#', '', 1, 'F', '0', 'system:notice:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1040, '操作查询', 500, 1, '#', '', 1, 'F', '0', 'monitor:operlog:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1041, '操作删除', 500, 2, '#', '', 1, 'F', '0', 'monitor:operlog:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1042, '日志导出', 500, 4, '#', '', 1, 'F', '0', 'monitor:operlog:export', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1043, '登录查询', 501, 1, '#', '', 1, 'F', '0', 'monitor:logininfor:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1044, '登录删除', 501, 2, '#', '', 1, 'F', '0', 'monitor:logininfor:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1045, '日志导出', 501, 3, '#', '', 1, 'F', '0', 'monitor:logininfor:export', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1046, '在线查询', 109, 1, '#', '', 1, 'F', '0', 'monitor:online:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1047, '批量强退', 109, 2, '#', '', 1, 'F', '0', 'monitor:online:batchLogout', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1048, '单条强退', 109, 3, '#', '', 1, 'F', '0', 'monitor:online:forceLogout', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1049, '任务查询', 110, 1, '#', '', 1, 'F', '0', 'monitor:job:query', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1050, '任务新增', 110, 2, '#', '', 1, 'F', '0', 'monitor:job:add', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1051, '任务修改', 110, 3, '#', '', 1, 'F', '0', 'monitor:job:edit', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1052, '任务删除', 110, 4, '#', '', 1, 'F', '0', 'monitor:job:remove', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1053, '状态修改', 110, 5, '#', '', 1, 'F', '0', 'monitor:job:changeStatus', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1054, '任务导出', 110, 7, '#', '', 1, 'F', '0', 'monitor:job:export', '#', 'admin', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2000, '设备管理', 0, 1, 'device', NULL, 1, 'M', '0', NULL, 'checkbox', 'admin', '2020-11-10 10:52:07', 'admin', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2001, '设备信息', 2000, 1, 'device', 'device/info/index', 1, 'C', '0', 'device:info:list', 'chart', 'admin', '2020-11-10 10:52:58', 'admin', '2021-03-29 17:36:07', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2002, '列表', 2001, 1, '', '', 1, 'F', '0', 'device:info:query', '#', 'admin', '2020-11-10 10:53:22', 'admin', '2020-11-10 10:53:22', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2003, '添加', 2001, 2, '', '', 1, 'F', '0', 'device:info:add', '#', 'admin', '2020-11-10 10:53:39', 'admin', '2020-11-10 10:53:39', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2004, '修改', 2001, 3, '', '', 1, 'F', '0', 'device:info:edit', '#', 'admin', '2020-11-10 10:53:52', 'admin', '2020-11-10 10:53:52', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2005, '删除', 2001, 4, '', '', 1, 'F', '0', 'device:info:remove', '#', 'admin', '2020-11-10 10:54:09', 'admin', '2020-11-10 10:54:09', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2006, '导出', 2001, 5, '', '', 1, 'F', '0', 'device:info:export', '#', 'admin', '2020-11-10 10:54:22', 'admin', '2020-11-10 10:54:22', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2007, '终端连接', 2040, 3, 'acquisitionMiddleware', 'acquisitionMiddleware/index', 1, 'C', '0', 'device:info:list', 'build', 'admin', '2021-01-29 17:03:23', 'master009', '2021-10-30 11:55:32', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2008, '列表', 2007, 1, '', '', 1, 'F', '0', 'device:info:list', '#', 'admin', '2021-01-29 17:04:41', 'admin', '2021-01-29 17:04:41', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2009, '连接日志', 2040, 4, 'ConnectLog', 'acquisitionMiddleware/log', 1, 'C', '0', 'device:info:list', 'clipboard', 'admin', '2021-01-29 22:03:10', 'master009', '2021-10-30 12:08:42', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2010, '列表', 2009, 1, '', '', 1, 'F', '0', 'device:info:list', '#', 'admin', '2021-01-29 22:03:38', 'admin', '2021-01-29 22:03:38', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2011, '传输管理', 0, 3, 'transfer', '', 1, 'M', '0', '', 'dict', 'admin', '2021-03-09 11:43:40', 'admin', '2021-03-24 10:29:55', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2012, '设备传输', 2011, 1, 'TransferAttribute', 'device/transfer/index', 1, 'C', '0', 'transfer:attribute:list', 'build', 'admin', '2021-03-09 14:31:26', 'admin', '2021-03-29 17:36:22', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2013, '列表', 2012, 1, '', '', 1, 'F', '0', 'transfer:attribute:list', '#', 'admin', '2021-03-09 14:32:55', 'admin', '2021-03-09 14:32:55', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2014, '修改', 2012, 2, '', '', 1, 'F', '0', 'transfer:attribute:edit', '#', 'admin', '2021-03-09 14:54:10', 'admin', '2021-03-09 14:54:10', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2015, '快照管理', 0, 2, 'snapshot', '', 1, 'M', '0', '', 'icon', 'admin', '2021-03-24 10:29:37', 'admin', '2021-03-24 10:29:37', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2016, '快照通道', 2015, 1, 'SnapshotChannel', 'snapshot/channel/index', 1, 'C', '0', 'snapshot:channel:query', 'color', 'admin', '2021-03-24 10:31:27', 'admin', '2021-03-24 10:31:27', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2017, '列表', 2016, 1, '', '', 1, 'F', '0', 'snapshot:channel:list', '#', 'admin', '2021-03-24 10:31:57', 'admin', '2021-03-24 10:31:57', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2018, '添加', 2016, 2, '', '', 1, 'F', '0', 'snapshot:channel:add', '#', 'admin', '2021-03-24 10:32:13', 'admin', '2021-03-24 10:32:13', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2019, '修改', 2016, 3, '', '', 1, 'F', '0', 'snapshot:channel:edit', '#', 'admin', '2021-03-24 10:32:38', 'admin', '2021-03-24 10:32:38', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2020, '删除', 2016, 4, '', '', 1, 'F', '0', 'snapshot:channel:remove', '#', 'admin', '2021-03-24 10:32:56', 'admin', '2021-03-24 10:32:56', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2021, '导出', 2016, 5, '', '', 1, 'F', '0', 'snapshot:channel:export', '#', 'admin', '2021-03-24 10:33:13', 'admin', '2021-03-24 10:33:13', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2022, '快照传输', 2011, 2, 'SnapshotTransfer', 'snapshot/transfer/index', 1, 'C', '0', 'snapshot:transfer:list', 'color', 'admin', '2021-03-24 10:34:06', 'admin', '2021-09-10 11:22:40', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2023, '列表', 2022, 1, '', '', 1, 'F', '0', 'snapshot:transfer:query', '#', 'admin', '2021-03-24 10:34:36', 'admin', '2021-09-10 11:22:45', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2024, '添加', 2022, 2, '', '', 1, 'F', '0', 'snapshot:transfer:add', '#', 'admin', '2021-03-24 10:34:58', 'admin', '2021-03-24 10:34:58', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2025, '修改', 2022, 3, '', '', 1, 'F', '0', 'snapshot:transfer:edit', '#', 'admin', '2021-03-24 10:35:17', 'admin', '2021-03-24 10:35:17', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2026, '删除', 2022, 4, '', '', 1, 'F', '0', 'snapshot:transfer:remove', '#', 'admin', '2021-03-24 10:35:36', 'admin', '2021-03-24 10:35:36', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2027, '导出', 2022, 5, '', '', 1, 'F', '0', 'snapshot:transfer:export', '#', 'admin', '2021-03-24 10:35:55', 'admin', '2021-03-24 10:35:55', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2028, '截屏日志', 2015, 2, 'SnapshotLog', 'snapshot/log/index', 1, 'C', '0', 'snapshot:log:list', 'dashboard', 'admin', '2021-03-24 10:36:41', 'admin', '2021-03-24 10:36:41', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2029, '列表', 2028, 1, '', '', 1, 'F', '0', 'snapshot:log:list', '#', 'admin', '2021-03-24 10:36:52', 'admin', '2021-03-24 10:36:52', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2030, '导出', 2028, 2, '', '', 1, 'F', '0', 'snapshot:log:export', '#', 'admin', '2021-03-24 10:37:05', 'admin', '2021-03-24 10:37:05', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2031, '启用', 2001, 6, '', NULL, 1, 'F', '0', 'device:info:enable', '#', 'admin', '2021-09-10 10:23:02', 'admin', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2032, '禁用', 2001, 7, '', NULL, 1, 'F', '0', 'device:info:disable', '#', 'admin', '2021-09-10 10:23:28', 'admin', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2033, '采集节点配置', 2000, 4, 'config', 'acquisitionMiddleware/config', 1, 'C', '1', 'device:info:list', 'edit', 'master009', '2021-10-21 15:41:05', 'master009', '2021-11-12 15:17:22', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2034, '串口配置查询', 112, 1, 'system:serial:query', NULL, 1, 'F', '0', 'system:serial:query', '#', 'master009', '2021-10-26 18:06:16', 'master009', '2021-10-26 18:08:10', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2035, '串口配置新增', 112, 2, '', NULL, 1, 'F', '0', 'system:serial:add', '#', 'master009', '2021-10-26 18:08:48', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2036, '串口配置修改', 112, 3, '', NULL, 1, 'F', '0', 'system:serial:edit', '#', 'master009', '2021-10-26 18:09:26', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2037, '串口配置删除', 112, 4, '', NULL, 1, 'F', '0', 'system:serial:remove', '#', 'master009', '2021-10-26 18:09:55', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2039, 'License授权管理', 1, 10, 'grant', 'system/grant/index', 1, 'C', '0', 'system:grant:list', 'validCode', 'master009', '2021-10-29 10:14:22', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2040, '采集终端', 0, 0, 'collect', NULL, 1, 'M', '0', NULL, 'dict', 'master009', '2021-10-30 11:13:00', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2041, '采集节点', 2040, 1, 'CollectNode', 'collect/node/index', 1, 'C', '0', 'collect:node:list', 'build', 'master009', '2021-10-30 11:18:01', 'master009', '2021-10-30 11:58:05', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2042, '列表', 2041, 1, '', NULL, 1, 'F', '0', 'collect:node:query', '#', 'master009', '2021-10-30 11:52:06', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2043, '添加', 2041, 2, '', NULL, 1, 'F', '0', 'collect:node:add', '#', 'master009', '2021-10-30 11:53:11', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2044, '修改', 2041, 3, '', NULL, 1, 'F', '0', 'collect:node:edit', '#', 'master009', '2021-10-30 11:53:39', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2045, '删除', 2041, 4, '', NULL, 1, 'F', '0', 'collect:node:remove', '#', 'master009', '2021-10-30 11:54:24', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2046, '采集模块', 2040, 2, 'CollectModule', 'collect/module/index', 1, 'C', '0', 'collect:module:list', 'build', 'master009', '2021-10-30 11:57:58', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2047, '列表', 2046, 1, '', NULL, 1, 'F', '0', 'collect:module:query', '#', 'master009', '2021-10-30 11:58:37', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2048, '新增', 2046, 2, '', NULL, 1, 'F', '0', 'collect:module:add', '#', 'master009', '2021-10-30 11:58:56', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2049, '修改', 2046, 3, '', NULL, 1, 'F', '0', 'collect:module:edit', '#', 'master009', '2021-10-30 11:59:25', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2050, '删除', 2046, 4, '', NULL, 1, 'F', '0', 'collect:module:remove', '#', 'master009', '2021-10-30 11:59:53', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2053, '修改', 2051, 2, '', NULL, 1, 'F', '0', 'system:network:edit', '#', 'master009', '2022-01-13 11:41:38', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2052, '查询', 2051, 1, '', NULL, 1, 'F', '0', 'system:network:list', '#', 'master009', '2022-01-13 11:41:22', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2051, '网卡配置', 1, 11, 'network', 'system/network/index', 1, 'C', '0', 'system:network:list', 'documentation', 'master009', '2022-01-13 11:41:02', '', NULL, '');


-- ----------------------------
-- 6、初始化-角色和菜单关联表数据
-- ----------------------------
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 100);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 101);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 102);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 103);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 104);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 106);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 108);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 109);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 500);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 501);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1001);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1002);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1003);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1004);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1005);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1006);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1007);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1008);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1009);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1010);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1011);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1012);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1013);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1014);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1015);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1016);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1017);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1018);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1019);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1020);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1021);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1022);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1023);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1024);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1025);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1031);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1032);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1033);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1034);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1035);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1040);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1041);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1042);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1043);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1044);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1045);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1046);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1047);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1048);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2000);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2001);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2002);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2003);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2004);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2005);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2006);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2007);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2008);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2009);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2010);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2011);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2012);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2013);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2014);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2015);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2016);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2017);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2018);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2019);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2020);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2021);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2022);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2023);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2024);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2025);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2026);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2027);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2028);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2029);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2030);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2031);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2032);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2033);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2039);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2040);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2041);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2042);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2043);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2044);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2045);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2046);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2047);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2048);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2049);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2050);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2051);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2052);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2053);


-- ----------------------------
-- 7、初始化-角色和部门关联表数据
-- ----------------------------
insert into sys_role_dept values ('2', '100');

-- ----------------------------
-- 8、初始化-用户与岗位关联表数据
-- ----------------------------
insert into sys_user_post values ('1', '1');
insert into sys_user_post values ('2', '1');

-- ----------------------------
-- 9、初始化-字典类型
-- ----------------------------
insert into sys_dict_type values(1,  '用户性别', 'sys_user_sex',        '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '用户性别列表');
insert into sys_dict_type values(2,  '菜单状态', 'sys_show_hide',       '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '菜单状态列表');
insert into sys_dict_type values(3,  '系统开关', 'sys_normal_disable',  '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '系统开关列表');
insert into sys_dict_type values(4,  '任务状态', 'sys_job_status',      '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '任务状态列表');
insert into sys_dict_type values(5,  '任务分组', 'sys_job_group',       '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '任务分组列表');
insert into sys_dict_type values(6,  '系统是否', 'sys_yes_no',          '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '系统是否列表');
insert into sys_dict_type values(7,  '通知类型', 'sys_notice_type',     '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '通知类型列表');
insert into sys_dict_type values(8,  '通知状态', 'sys_notice_status',   '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '通知状态列表');
insert into sys_dict_type values(9,  '操作类型', 'sys_oper_type',       '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '操作类型列表');
insert into sys_dict_type values(10, '系统状态', 'sys_common_status',   '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '登录状态列表');
insert into sys_dict_type values(100, '设备类型', 'device_type',   '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '设备类型列表');
insert into sys_dict_type values(101, '连接方式', 'connect_type',   '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '连接方式列表');
insert into sys_dict_type values(102, '波特率', 'baud_rate',   '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '波特率');
insert into sys_dict_type values(103, '数据位', 'date_bits',   '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '数据位');
insert into sys_dict_type values(104, '停止位', 'stop_bits',   '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '停止位');
insert into sys_dict_type values(105, '校验位', 'parity',   '0', 'admin', '2018-03-16 11-33-00', 'admin', '2018-03-16 11-33-00', '校验位');

-- ----------------------------
-- 10、初始化-字典类型
-- ----------------------------
insert into sys_dict_data values(1,  1,  '男',       '0',       'sys_user_sex',        '',   '',        'Y', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '性别男');
insert into sys_dict_data values(2,  2,  '女',       '1',       'sys_user_sex',        '',   '',        'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '性别女');
insert into sys_dict_data values(3,  3,  '未知',     '2',       'sys_user_sex',        '',   '',        'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '性别未知');
insert into sys_dict_data values(4,  1,  '显示',     '0',       'sys_show_hide',       '',   'primary', 'Y', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '显示菜单');
insert into sys_dict_data values(5,  2,  '隐藏',     '1',       'sys_show_hide',       '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '隐藏菜单');
insert into sys_dict_data values(6,  1,  '正常',     '0',       'sys_normal_disable',  '',   'primary', 'Y', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '正常状态');
insert into sys_dict_data values(7,  2,  '停用',     '1',       'sys_normal_disable',  '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '停用状态');
insert into sys_dict_data values(8,  1,  '正常',     '0',       'sys_job_status',      '',   'primary', 'Y', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '正常状态');
insert into sys_dict_data values(9,  2,  '暂停',     '1',       'sys_job_status',      '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '停用状态');
insert into sys_dict_data values(10, 1,  '默认',     'DEFAULT', 'sys_job_group',       '',   '',        'Y', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '默认分组');
insert into sys_dict_data values(11, 2,  '系统',     'SYSTEM',  'sys_job_group',       '',   '',        'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '系统分组');
insert into sys_dict_data values(12, 1,  '是',       'Y',       'sys_yes_no',          '',   'primary', 'Y', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '系统默认是');
insert into sys_dict_data values(13, 2,  '否',       'N',       'sys_yes_no',          '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '系统默认否');
insert into sys_dict_data values(14, 1,  '通知',     '1',       'sys_notice_type',     '',   'warning', 'Y', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '通知');
insert into sys_dict_data values(15, 2,  '公告',     '2',       'sys_notice_type',     '',   'success', 'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '公告');
insert into sys_dict_data values(16, 1,  '正常',     '0',       'sys_notice_status',   '',   'primary', 'Y', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '正常状态');
insert into sys_dict_data values(17, 2,  '关闭',     '1',       'sys_notice_status',   '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '关闭状态');
insert into sys_dict_data values(18, 1,  '新增',     '1',       'sys_oper_type',       '',   'info',    'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '新增操作');
insert into sys_dict_data values(19, 2,  '修改',     '2',       'sys_oper_type',       '',   'info',    'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '修改操作');
insert into sys_dict_data values(20, 3,  '删除',     '3',       'sys_oper_type',       '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '删除操作');
insert into sys_dict_data values(21, 4,  '授权',     '4',       'sys_oper_type',       '',   'primary', 'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '授权操作');
insert into sys_dict_data values(22, 5,  '导出',     '5',       'sys_oper_type',       '',   'warning', 'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '导出操作');
insert into sys_dict_data values(23, 6,  '导入',     '6',       'sys_oper_type',       '',   'warning', 'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '导入操作');
insert into sys_dict_data values(24, 7,  '强退',     '7',       'sys_oper_type',       '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '强退操作');
insert into sys_dict_data values(25, 8,  '生成代码', '8',       'sys_oper_type',       '',   'warning', 'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '生成操作');
insert into sys_dict_data values(26, 9,  '清空数据', '9',       'sys_oper_type',       '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '清空操作');
insert into sys_dict_data values(27, 1,  '成功',     '0',       'sys_common_status',   '',   'primary', 'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '正常状态');
insert into sys_dict_data values(28, 2,  '失败',     '1',       'sys_common_status',   '',   'danger',  'N', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '停用状态');
INSERT INTO sys_dict_data VALUES (121, '0', '分辨率1', '640*360', 'resolving_power', null, null, 'N', '0', '', null, '', null, null);
INSERT INTO sys_dict_data VALUES ('122', '1', '分辨率2', '1280*720', 'resolving_power', null, null, 'N', '0', '', null, '', null, null);
INSERT INTO sys_dict_data VALUES ('123', '2', '分辨率3', '1920*1080', 'resolving_power', null, null, 'N', '0', '', null, '', null, null);
INSERT INTO sys_dict_data VALUES ('100', '0', 'GPS', '32', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:56:01', 'admin', '2021-04-19 16:41:34', '星站差分GPS ，优先传输通道');
INSERT INTO sys_dict_data VALUES ('101', '1', 'ATTITUDE', '33', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:56:21', 'admin', '2020-04-28 14:35:55', '姿态仪');
INSERT INTO sys_dict_data VALUES ('102', '2', 'SEAPATH', '34', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:56:42', 'admin', '2020-04-28 14:35:59', '声学GPS');
INSERT INTO sys_dict_data VALUES ('103', '4', 'EA600', '35', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:57:04', 'admin', '2020-04-28 14:36:03', '水深');
INSERT INTO sys_dict_data VALUES ('104', '3', 'WINCH', '36', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:57:22', 'admin', '2020-04-28 14:36:07', '绞车');
INSERT INTO sys_dict_data VALUES ('105', '5', 'SBE21', '37', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:57:42', 'admin', '2020-04-28 14:36:12', '走航SBE21');
INSERT INTO sys_dict_data VALUES ('106', '6', 'AWS', '38', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:57:57', 'admin', '2020-04-28 14:36:17', '气象站');
INSERT INTO sys_dict_data VALUES ('107', '7', 'GO8050', '39', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:58:19', 'admin', '2020-04-28 14:36:20', '走航CO2');
INSERT INTO sys_dict_data VALUES ('108', '8', 'LOG', '40', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:58:34', 'admin', '2020-04-28 14:36:23', '计程仪');
INSERT INTO sys_dict_data VALUES ('109', '9', 'ECHO', '41', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:58:47', 'admin', '2020-04-28 14:36:27', '测深（浅水）');
INSERT INTO sys_dict_data VALUES ('110', '10', 'COMPASS', '42', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:03', 'admin', '2021-04-20 17:25:32', '罗经');
INSERT INTO sys_dict_data VALUES ('111', '11', 'DGPS1', '43', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:17', 'admin', '2020-04-28 14:36:34', '船载GPS1');
INSERT INTO sys_dict_data VALUES ('112', '12', 'DGPS2', '44', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:32', 'admin', '2020-04-28 14:36:37', '船载GPS2');
INSERT INTO sys_dict_data VALUES ('113', '13', 'AIS', '45', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:45', 'admin', '2020-04-28 14:36:46', '船载AIS');
INSERT INTO sys_dict_data VALUES ('114', '14', 'WIND', '46', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:56', 'admin', '2020-04-28 14:36:49', '船载风速风向');
INSERT INTO sys_dict_data VALUES ('115', '15', 'RA-CDU1', '47', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 19:00:09', 'admin', '2020-04-28 14:36:53', '手动舵1');
INSERT INTO sys_dict_data VALUES ('116', '16', 'RA-CDU2', '48', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 19:00:22', 'admin', '2020-04-28 14:36:56', '手动舵2');
INSERT INTO sys_dict_data VALUES ('117', '17', 'AUTO PIOLT', '49', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 19:00:34', 'admin', '2020-04-28 14:37:00', '自动舵');
INSERT INTO sys_dict_data VALUES ('118', '18', 'GYRO', '50', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 19:00:46', 'admin', '2020-04-28 14:37:03', '磁罗经');
INSERT INTO sys_dict_data VALUES ('119', '52', 'SEAPT', '52', 'device_type', null, null, 'N', '0', 'admin', '2021-01-29 17:39:03', '', null, null);
INSERT INTO sys_dict_data VALUES ('120', '51', 'WINCH', '51', 'device_type', null, null, 'N', '0', 'admin', '2021-01-29 17:39:13', '', null, null);
 
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('142', '17', '1200', '1200', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:55:56', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('141', '16', '2400', '2400', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:55:43', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('140', '15', '4800', '4800', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:55:31', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('139', '14', '9600', '9600', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:55:15', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('138', '13', '14400', '14400', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:55:01', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('137', '12', '19200', '19200', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:54:49', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('136', '11', '38400', '38400', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:54:38', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('135', '10', '43000', '43000', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:54:25', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('134', '9', '57600', '57600', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:53:59', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('133', '8', '76800', '76800', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:53:44', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('132', '7', '115200', '115200', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:53:29', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('131', '6', '128000', '128000', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:53:16', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('130', '5', '230400', '230400', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:53:02', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('129', '4', '256000', '256000', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:52:48', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('128', '3', '460800', '460800', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:52:34', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('127', '2', '921600', '921600', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:52:18', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('126', '1', '1382400', '1382400', 'baud_rate', NULL, NULL, 'N', '0', 'master009', '2021-10-16 16:52:02', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('125', '2', '串口连接', '1', 'connect_type', NULL, NULL, 'N', '0', 'admin', '2021-10-15 10:16:47', '', NULL, '串口连接');
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('124', '1', '采集终端', '0', 'connect_type', NULL, NULL, 'N', '0', 'admin', '2021-10-15 10:16:09', 'admin', '2021-10-15 10:16:23', '采集终端');
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('154', '5', '校验位始终为0', '4', 'parity', NULL, NULL, 'N', '0', 'master009', '2021-10-19 15:00:25', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('153', '4', '校验位始终为1', '3', 'parity', NULL, NULL, 'N', '0', 'master009', '2021-10-19 15:00:05', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('152', '3', '偶校验（EVEN）', '2', 'parity', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:57:37', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('151', '2', '奇校验（ODD）', '1', 'parity', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:56:51', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('150', '1', '没有校验（NONE）', '0', 'parity', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:56:30', 'master009', '2021-10-19 14:57:05', NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('149', '3', '2', '2', 'stop_bits', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:54:56', 'master009', '2021-10-19 14:55:09', NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('147', '1', '1', '1', 'stop_bits', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:54:40', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('146', '4', '8', '8', 'date_bits', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:53:44', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('145', '3', '7', '7', 'date_bits', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:53:36', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('144', '2', '6', '6', 'date_bits', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:53:29', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('143', '1', '5', '5', 'date_bits', NULL, NULL, 'N', '0', 'master009', '2021-10-19 14:53:22', '', NULL, NULL);

-- ----------------------------
-- 11、初始化-参数配置表
-- ----------------------------
insert into sys_config values(1, '主框架页-默认皮肤样式名称', 'sys.index.skinName',     'skin-blue',     'Y', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
insert into sys_config values(2, '用户管理-账号初始密码',     'sys.user.initPassword',  '123456',        'Y', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '初始化密码123456');
insert into sys_config values(3, '主框架页-侧边栏主题',       'sys.index.sideTheme',    'theme-dark',    'Y', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '深色主题theme-dark，浅色主题theme-light');
insert into sys_config values(4, '岸端服务器IP',       'sys.laputa.ip',    '************',    'Y', 'admin', '2021-06-16 11-33-00', 'admin', '2021-06-16 11-33-00', '岸端服务器IP');
insert into sys_config values(5, '岸端服务器UDP服务端口',       'sys.laputa.port',    '10003',    'Y', 'admin', '2021-06-16 11-33-00', 'admin', '2021-06-16 11-33-00', '岸端服务器UDP服务端口');

-- ----------------------------
-- 12、初始化-定时任务调度表
-- ----------------------------
insert into sys_job values(1, '系统默认（无参）', 'DEFAULT', 'scheduledTask.noParams','0/10 * * * * ?', '3', '1', '1','0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_job values(2, '系统默认（有参）', 'DEFAULT', 'scheduledTask.haveParams(\'xhjt\')','0/15 * * * * ?', '3', '1', '1','0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_job values(3, '系统默认（多参）', 'DEFAULT', 'scheduledTask.multipleParams(\'xhjt\', true, 2000L, 316.50D, 100)',  '0/20 * * * * ?', '3', '1', '1','0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_job values(4, '定时同步设备、快照信息到岸端', 'DEFAULT', 'scheduledTask.sys2Msg','0 */5 * * * ?', '3', '1', '0','0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_job values(5, '定时同步船舶终端管理信息', 'DEFAULT', 'scheduledTask.ship2Msg','0 */5 * * * ?', '3', '1', '0','0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');

-- ----------------------------
-- 13、初始化-串口名称配置表
-- ----------------------------
INSERT INTO `dct`.`serial_config`(`id`, `old_name`, `new_name`, `create_by`, `create_time`, `update_by`, `update_time`, `enable`, `remark`) VALUES (500, '/dev/ttyS1', 'COM2', 'master009', '2021-10-26 18:48:49', '', NULL, 1, 'COM2');
INSERT INTO `dct`.`serial_config`(`id`, `old_name`, `new_name`, `create_by`, `create_time`, `update_by`, `update_time`, `enable`, `remark`) VALUES (501, '/dev/ttyS2', 'COM3', 'master009', '2021-10-26 18:56:30', '', NULL, 1, 'COM3');
INSERT INTO `dct`.`serial_config`(`id`, `old_name`, `new_name`, `create_by`, `create_time`, `update_by`, `update_time`, `enable`, `remark`) VALUES (502, '/dev/ttyS3', 'COM4', 'master009', '2021-10-26 18:56:42', '', NULL, 1, 'COM4');
INSERT INTO `dct`.`serial_config`(`id`, `old_name`, `new_name`, `create_by`, `create_time`, `update_by`, `update_time`, `enable`, `remark`) VALUES (503, '/dev/ttyS0', 'COM1', 'master009', '2021-10-26 18:57:14', '', NULL, 1, 'COM1');
INSERT INTO `dct`.`serial_config`(`id`, `old_name`, `new_name`, `create_by`, `create_time`, `update_by`, `update_time`, `enable`, `remark`) VALUES (504, '/dev/ttyS4', 'COM5', 'master009', '2022-03-25 11:59:03', '', NULL, 1, 'COM5');
INSERT INTO `dct`.`serial_config`(`id`, `old_name`, `new_name`, `create_by`, `create_time`, `update_by`, `update_time`, `enable`, `remark`) VALUES (505, '/dev/ttyS5', 'COM6', 'master009', '2022-03-25 11:59:16', '', NULL, 1, 'COM6');

-- ----------------------------
-- 14、初始化-设备传输属性默认数据
-- ----------------------------
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (1, 42, 'hehdt');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (2, 42, 'herot');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (3, 46, 'relativeWind');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (4, 46, 'windLogoR');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (5, 46, 'relativeWindSpeed');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (6, 46, 'trueWind');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (7, 46, 'trueWindSpeed');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (8, 46, 'windLogoT');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (9, 32, 'utcTime');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (10, 32, 'latitudeHemisphere');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (11, 32, 'longitudeHemisphere');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (12, 32, 'latitude');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (13, 32, 'longitude');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (14, 32, 'groundRateJ');
INSERT INTO `dct`.`default_attribute`(`id`, `type`, `name`) VALUES (15, 32, 'groundRateKm');

-- ----------------------------
-- 15、内置GPS 罗经
-- ----------------------------
INSERT INTO `dct`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '0', '内置GPS', '53', 'device_type', NULL, NULL, 'N', '0', 'admin', '2022-07-02 14:53:22', '', NULL, '内置GPS');
INSERT INTO `dct`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '10', '内置COMPASS', '54', 'device_type', NULL, NULL, 'N', '0', 'admin', '2022-07-02 14:53:22', '', NULL, '内置COMPASS');
